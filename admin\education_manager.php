<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

$action = $_POST['action'] ?? '';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'add_education':
            $new_item = [
                'title' => $_POST['title'],
                'institution' => $_POST['institution'],
                'year' => $_POST['year']
            ];
            $data['education'][] = $new_item;
            $success_message = "Education record added successfully!";
            break;
            
        case 'edit_education':
            $index = (int)$_POST['index'];
            if (isset($data['education'][$index])) {
                $data['education'][$index] = [
                    'title' => $_POST['title'],
                    'institution' => $_POST['institution'],
                    'year' => $_POST['year']
                ];
                $success_message = "Education record updated successfully!";
            } else {
                $error_message = "Education record not found!";
            }
            break;
            
        case 'delete_education':
            $index = (int)$_POST['index'];
            if (isset($data['education'][$index])) {
                array_splice($data['education'], $index, 1);
                $success_message = "Education record deleted successfully!";
            } else {
                $error_message = "Education record not found!";
            }
            break;
    }
    
    // Save updated data
    if ($success_message) {
        // Create backup
        $backup_file = '../data/backups/site-data-' . date('Y-m-d-H-i-s') . '.json';
        if (!is_dir('../data/backups')) {
            mkdir('../data/backups', 0755, true);
        }
        copy($json_file, $backup_file);
        
        // Save new data
        file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Education Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-light bg-light">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="dashboard_new.php">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <h4 class="mb-0">Education Management</h4>
                    </div>
                </nav>
                
                <div class="container mt-4">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <h3>Education Management</h3>
                    <p class="text-muted">Total Education Records: <?php echo count($data['education']); ?></p>

                    <!-- Add New Education Form -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-plus"></i> Add New Education Record</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="add_education">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title/Degree *</label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="institution" class="form-label">Institution *</label>
                                            <input type="text" class="form-control" id="institution" name="institution" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="year" class="form-label">Year/Period *</label>
                                            <input type="text" class="form-control" id="year" name="year" required>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Education Record
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Existing Education Records -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Existing Education Records (<?php echo count($data['education']); ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($data['education'])): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No education records found. Add your first education record using the form above.
                                </div>
                            <?php else: ?>
                                <?php foreach ($data['education'] as $index => $education): ?>
                                    <div class="border p-3 mb-3 rounded">
                                        <form method="POST" class="mb-3">
                                            <input type="hidden" name="action" value="edit_education">
                                            <input type="hidden" name="index" value="<?php echo $index; ?>">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Title/Degree</label>
                                                        <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($education['title']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Institution</label>
                                                        <input type="text" class="form-control" name="institution" value="<?php echo htmlspecialchars($education['institution']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Year/Period</label>
                                                        <input type="text" class="form-control" name="year" value="<?php echo htmlspecialchars($education['year']); ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-save"></i> Update
                                                </button>
                                            </div>
                                        </form>
                                        
                                        <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this education record?')">
                                            <input type="hidden" name="action" value="delete_education">
                                            <input type="hidden" name="index" value="<?php echo $index; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
