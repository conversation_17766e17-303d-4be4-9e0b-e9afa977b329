<?php
// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $thesis = $dataLoader->getThesis();
    $seo = $dataLoader->getMetaTags();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta Debbarma'];
    $thesis = [];
    $seo = ['title' => 'Thesis - Dr. Jayanta Debbarma'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?> - Thesis & Research</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Responsive CSS -->
    <link rel="stylesheet" href="assets/css/responsive.css">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e67e22;
            --accent-light: #f39c12;
            --success-color: #27ae60;
            --text-color: #2c3e50;
            --text-light: #7f8c8d;
            --light-gray: #ecf0f1;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            --gradient-accent: linear-gradient(45deg, #e67e22, #f39c12);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--light-gray);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 100px 0 50px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .back-btn {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Main Content */
        .main-content {
            padding: 80px 0;
        }
        
        /* Bookshelf Section */
        .bookshelf-section {
            background: var(--white);
            padding: 80px 0;
            border-radius: 20px;
            margin-bottom: 50px;
            position: relative;
            overflow: hidden;
        }
        
        .bookshelf-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="wood" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="%23f4f4f4"/><path d="M0 10h20M0 20h20" stroke="%23e0e0e0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23wood)"/></svg>');
            opacity: 0.3;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
        }
        
        .bookshelf {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            perspective: 1000px;
            position: relative;
            z-index: 2;
        }
        
        .book-slot {
            position: relative;
            width: 300px;
            height: 450px;
            margin: 20px;
        }
        
        .book-cover {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transition: transform 0.8s ease;
            cursor: pointer;
        }
        
        .book-slot:hover .book-cover {
            transform: rotateY(180deg);
        }
        
        .book-front, .book-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .book-front {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            color: var(--white);
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .book-front::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .book-back {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            color: var(--text-color);
            transform: rotateY(180deg);
            padding: 40px;
            display: flex;
            flex-direction: column;
        }
        
        .book-spine {
            position: absolute;
            left: -10px;
            top: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(to bottom, #1a252f, #2c3e50);
            border-radius: 10px 0 0 10px;
            z-index: -1;
        }
        
        .spine-text {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            color: var(--white);
            font-size: 0.9rem;
            font-weight: 600;
            padding: 30px 3px;
            text-align: center;
        }
        
        .book-header {
            text-align: center;
            padding: 30px 20px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }
        
        .university-logo {
            width: 60px;
            height: 60px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 28px;
        }
        
        .university-name {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.9;
        }
        
        .book-content {
            flex: 1;
            padding: 40px 25px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .book-title {
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.3;
        }
        
        .book-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            font-style: italic;
            margin-bottom: 20px;
        }
        
        .book-footer {
            padding: 25px;
            border-top: 2px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .book-author {
            font-size: 1rem;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .book-year {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 8px;
        }
        
        .book-degree {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .book-back-content {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .book-back h4 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .book-abstract {
            flex: 1;
            font-size: 1rem;
            line-height: 1.7;
            margin-bottom: 25px;
        }
        
        .supervisor-info {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(52, 73, 94, 0.1);
            border-radius: 10px;
            font-size: 1rem;
        }
        
        .book-actions {
            text-align: center;
        }
        
        .book-link {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--gradient-accent);
            color: var(--white);
            text-decoration: none;
            padding: 15px 25px;
            border-radius: 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .book-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(230, 126, 34, 0.4);
        }
        
        /* Research Details */
        .research-details {
            margin-top: 80px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }
        
        .detail-card {
            background: var(--white);
            padding: 40px;
            border-radius: 15px;
            box-shadow: var(--shadow-light);
            border-left: 5px solid var(--accent-color);
        }
        
        .detail-card h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        
        .detail-card p {
            line-height: 1.7;
            margin-bottom: 15px;
        }
        
        .detail-list {
            list-style: none;
            margin-top: 20px;
        }
        
        .detail-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
        }
        
        .detail-list li:last-child {
            border-bottom: none;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .bookshelf {
                flex-direction: column;
                align-items: center;
            }
            
            .book-slot {
                width: 280px;
                height: 400px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .back-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-flex;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Main Page
        </a>
        <div class="container">
            <h1>Thesis & Research</h1>
            <p>Comprehensive academic research and scholarly contributions</p>
            <div style="margin-top: 30px;">
                <a href="about-more.php" style="color: white; text-decoration: none; margin: 0 15px;">About</a>
                <a href="journey-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Journey</a>
                <a href="publications-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Publications</a>
                <a href="advisors-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Advisors</a>
                <a href="gallery-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Gallery</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Bookshelf Section -->
            <div class="bookshelf-section" data-aos="fade-up">
                <h2 class="section-title">Research Library</h2>
                <div class="bookshelf">
                    <?php echo renderThesisBookshelf($thesis); ?>
                </div>
            </div>

            <!-- Research Details -->
            <div class="research-details" data-aos="fade-up">
                <h2 class="section-title">Research Overview</h2>
                <div class="details-grid">
                    <div class="detail-card" data-aos="zoom-in">
                        <h3>Research Focus Areas</h3>
                        <p>My research spans multiple disciplines within engineering, with particular emphasis on sustainable technologies and innovative materials.</p>
                        <ul class="detail-list">
                            <li><span>Advanced Materials</span><span>Primary Focus</span></li>
                            <li><span>Sustainable Engineering</span><span>Core Research</span></li>
                            <li><span>Technology Innovation</span><span>Applied Research</span></li>
                            <li><span>Environmental Impact</span><span>Cross-cutting Theme</span></li>
                        </ul>
                    </div>
                    
                    <div class="detail-card" data-aos="zoom-in" data-aos-delay="100">
                        <h3>Methodology & Approach</h3>
                        <p>Employing interdisciplinary approaches that combine theoretical foundations with practical applications and real-world testing.</p>
                        <ul class="detail-list">
                            <li><span>Experimental Design</span><span>Laboratory Studies</span></li>
                            <li><span>Computational Modeling</span><span>Simulation Analysis</span></li>
                            <li><span>Field Testing</span><span>Real-world Validation</span></li>
                            <li><span>Collaborative Research</span><span>Industry Partnerships</span></li>
                        </ul>
                    </div>
                    
                    <div class="detail-card" data-aos="zoom-in" data-aos-delay="200">
                        <h3>Impact & Applications</h3>
                        <p>Research outcomes have been applied in various industries, contributing to technological advancement and sustainable development.</p>
                        <ul class="detail-list">
                            <li><span>Patent Applications</span><span>15+ Filed</span></li>
                            <li><span>Industry Collaborations</span><span>20+ Projects</span></li>
                            <li><span>Technology Transfer</span><span>5+ Licenses</span></li>
                            <li><span>Student Supervision</span><span>30+ Graduates</span></li>
                        </ul>
                    </div>
                    
                    <div class="detail-card" data-aos="zoom-in" data-aos-delay="300">
                        <h3>Future Directions</h3>
                        <p>Ongoing and planned research initiatives that will shape the future of engineering and technology development.</p>
                        <ul class="detail-list">
                            <li><span>AI Integration</span><span>Emerging Focus</span></li>
                            <li><span>Green Technology</span><span>Sustainability Goals</span></li>
                            <li><span>Smart Materials</span><span>Next Generation</span></li>
                            <li><span>Global Challenges</span><span>Societal Impact</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
