# 🚀 Deployment Guide - Dr<PERSON> <PERSON><PERSON>rma Portfolio

## 📋 **What to Share/Upload to Server**

### **Essential Files & Folders**
```
📁 Your Project Root/
├── 📁 admin/                    # Complete admin system
├── 📁 assets/                   # Images, documents, CSS, JS
├── 📁 data/                     # JSON data files
├── 📁 includes/                 # PHP classes and helpers
├── 📄 index.php                 # Main homepage
├── 📄 thesis-more.php           # Thesis details page
├── 📄 publications-more.php     # Publications page
├── 📄 advisors-more.php         # Advisors page
├── 📄 gallery-more.php          # Gallery page
├── 📄 visitor_counter.php       # Visitor tracking
└── 📄 .htaccess                 # URL rewriting (if exists)
```

### **Database Requirements**
- **Database Name**: `dr_jd_2.0` (your existing database)
- **Required Tables**: 
  - Your existing visitor counter table
  - `admin_users` (will be created by setup)
  - `admin_sessions` (will be created by setup)

---

## 🖥️ **Server Requirements**

### **Minimum Requirements**
- **PHP**: 7.4 or higher (8.0+ recommended)
- **MySQL**: 5.7 or higher (8.0+ recommended)
- **Web Server**: Apache or Nginx
- **Storage**: 500MB minimum (for files and uploads)

### **PHP Extensions Required**
- `pdo_mysql` - Database connectivity
- `fileinfo` - File type detection
- `gd` or `imagick` - Image processing
- `json` - JSON handling
- `session` - Session management
- `mbstring` - String handling

---

## 📊 **Database Setup**

### **Option 1: Use Existing Database**
1. **Keep your existing `dr_jd_2.0` database**
2. **Update database credentials** in `admin/config/database.php`:
   ```php
   $host = 'localhost';           // Your server's DB host
   $username = 'your_db_user';    // Your database username
   $password = 'your_db_password'; // Your database password
   $dbname = 'dr_jd_2.0';         // Keep existing database name
   ```

### **Option 2: Export/Import Database**
If moving to a new server:
```sql
-- Export from old server
mysqldump -u username -p dr_jd_2.0 > dr_jd_2.0_backup.sql

-- Import to new server
mysql -u username -p dr_jd_2.0 < dr_jd_2.0_backup.sql
```

---

## 🔧 **Installation Steps**

### **Step 1: Upload Files**
1. **Upload all project files** to your web server
2. **Set proper permissions**:
   ```bash
   chmod 755 admin/
   chmod 644 admin/*.php
   chmod 755 data/
   chmod 666 data/site-data.json
   chmod 755 assets/
   ```

### **Step 2: Configure Database**
1. **Edit** `admin/config/database.php`
2. **Update credentials** for your server
3. **Test connection** by visiting `admin/check_database.php`

### **Step 3: Setup Admin System**
1. **Visit**: `https://yourdomain.com/admin/setup_database.php`
2. **This will create**:
   - `admin_users` table
   - `admin_sessions` table
   - Default admin user
3. **Default credentials**:
   - Email: `<EMAIL>`
   - Password: `admin123`
   - **⚠️ CHANGE IMMEDIATELY AFTER FIRST LOGIN**

### **Step 4: Test Everything**
1. **Frontend**: `https://yourdomain.com/`
2. **Admin Login**: `https://yourdomain.com/admin/`
3. **File Upload**: Test upload functionality
4. **Data Management**: Test CRUD operations

---

## 🔒 **Security Configuration**

### **Change Default Credentials**
1. **Login** to admin panel
2. **Click user dropdown** → "Change Password"
3. **Set strong password**
4. **Update email** if needed

### **File Permissions**
```bash
# Directories
find . -type d -exec chmod 755 {} \;

# PHP files
find . -name "*.php" -exec chmod 644 {} \;

# Data files
chmod 666 data/site-data.json
chmod 755 data/backups/

# Upload directories
chmod 755 assets/documents/
chmod 755 assets/images/
```

### **Secure Configuration**
1. **Remove setup files** after installation:
   ```bash
   rm admin/setup_database.php
   rm admin/check_database.php
   ```

2. **Add IP restrictions** (optional) in `.htaccess`:
   ```apache
   <Files "admin/*">
       Order Deny,Allow
       Deny from all
       Allow from YOUR_IP_ADDRESS
   </Files>
   ```

---

## 🌐 **Domain Configuration**

### **Update URLs in Data**
1. **Edit** `data/site-data.json`
2. **Update** any hardcoded URLs:
   ```json
   {
     "website_settings": {
       "seo": {
         "canonical_url": "https://yourdomain.com"
       }
     }
   }
   ```

### **SEO Configuration**
1. **Visit**: `https://yourdomain.com/admin/seo_manager.php`
2. **Update**:
   - Canonical URL
   - Meta descriptions
   - Social media URLs
   - Analytics codes

---

## 📁 **File Structure After Deployment**

```
📁 public_html/ (or www/)
├── 📁 admin/
│   ├── 📁 classes/
│   ├── 📁 config/
│   ├── 📁 sections/
│   ├── 📄 dashboard_new.php
│   ├── 📄 login.php
│   ├── 📄 file_upload.php
│   ├── 📄 seo_manager.php
│   └── 📄 ...
├── 📁 assets/
│   ├── 📁 css/
│   ├── 📁 js/
│   ├── 📁 images/
│   └── 📁 documents/
├── 📁 data/
│   ├── 📄 site-data.json
│   └── 📁 backups/
├── 📁 includes/
│   ├── 📄 DataLoader.php
│   └── 📄 helpers.php
└── 📄 index.php
```

---

## 🔍 **Testing Checklist**

### **Frontend Testing**
- [ ] Homepage loads correctly
- [ ] All sections display data
- [ ] Navigation works
- [ ] Responsive design works
- [ ] Images load properly
- [ ] Contact forms work

### **Admin Testing**
- [ ] Login works
- [ ] Dashboard loads
- [ ] Data editing works
- [ ] File upload works
- [ ] SEO manager works
- [ ] Backup creation works

### **Security Testing**
- [ ] Admin requires login
- [ ] File uploads are secure
- [ ] CSRF protection works
- [ ] Session management works
- [ ] Rate limiting works

---

## 🆘 **Troubleshooting**

### **Common Issues**

#### **Database Connection Failed**
```
Solution: Check admin/config/database.php credentials
```

#### **File Upload Fails**
```
Solution: Check folder permissions (755 for directories)
```

#### **JSON Data Not Loading**
```
Solution: Check data/site-data.json permissions (666)
```

#### **Admin Login Not Working**
```
Solution: Run admin/setup_database.php again
```

### **Error Logs**
- **Check PHP error logs** on your server
- **Enable error reporting** temporarily:
  ```php
  ini_set('display_errors', 1);
  error_reporting(E_ALL);
  ```

---

## 📞 **Support**

### **Files to Check First**
1. `admin/config/database.php` - Database settings
2. `data/site-data.json` - Main data file
3. Server error logs
4. Browser console for JavaScript errors

### **Backup Strategy**
- **Database**: Export `dr_jd_2.0` regularly
- **Files**: Backup `data/` folder daily
- **Code**: Keep project files in version control

---

## 🎯 **Post-Deployment Tasks**

1. **Change admin password**
2. **Update SEO settings**
3. **Add Google Analytics**
4. **Test all functionality**
5. **Set up regular backups**
6. **Monitor error logs**

---

**🎉 Your portfolio is now ready for production!**
