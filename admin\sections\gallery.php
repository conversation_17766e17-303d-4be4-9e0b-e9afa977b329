<?php
// Include the file upload widget
require_once 'components/FileUploadWidget.php';
$uploadWidget = new FileUploadWidget();
?>

<h3>Gallery Management</h3>

<!-- Add New Gallery Item Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5>Add New Gallery Item</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="add_gallery">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-control" id="category" name="category" required>
                            <option value="research">Research</option>
                            <option value="projects">Projects</option>
                            <option value="documents">Documents</option>
                            <option value="videos">Videos</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-control" id="type" name="type" required onchange="updateUploadCategory()">
                            <option value="image">Image</option>
                            <option value="pdf">PDF</option>
                            <option value="video">Video</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="image" class="form-label">Thumbnail Image</label>
                        <?php echo $uploadWidget->render('gallery', 'image', '', 'gallery_thumbnail'); ?>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
            </div>
            <div class="mb-3">
                <label for="link" class="form-label">File/Link Path</label>
                <input type="text" class="form-control" id="link" name="link" required>
            </div>
            <button type="submit" class="btn btn-success">Add Gallery Item</button>
        </form>
    </div>
</div>

<!-- Existing Gallery Items -->
<div class="card">
    <div class="card-header">
        <h5>Existing Gallery Items</h5>
    </div>
    <div class="card-body">
        <?php foreach ($data['gallery'] as $index => $item): ?>
            <div class="border p-3 mb-3">
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="edit_gallery">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($item['title']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-control" name="category" required>
                                    <option value="research" <?php echo $item['category'] === 'research' ? 'selected' : ''; ?>>Research</option>
                                    <option value="projects" <?php echo $item['category'] === 'projects' ? 'selected' : ''; ?>>Projects</option>
                                    <option value="documents" <?php echo $item['category'] === 'documents' ? 'selected' : ''; ?>>Documents</option>
                                    <option value="videos" <?php echo $item['category'] === 'videos' ? 'selected' : ''; ?>>Videos</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Type</label>
                                <select class="form-control" name="type" required>
                                    <option value="image" <?php echo $item['type'] === 'image' ? 'selected' : ''; ?>>Image</option>
                                    <option value="pdf" <?php echo $item['type'] === 'pdf' ? 'selected' : ''; ?>>PDF</option>
                                    <option value="video" <?php echo $item['type'] === 'video' ? 'selected' : ''; ?>>Video</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Thumbnail Image Path</label>
                                <input type="text" class="form-control" name="image" value="<?php echo htmlspecialchars($item['image']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3" required><?php echo htmlspecialchars($item['description']); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">File/Link Path</label>
                        <input type="text" class="form-control" name="link" value="<?php echo htmlspecialchars($item['link']); ?>" required>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm">Update</button>
                    </div>
                </form>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="delete_gallery">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this gallery item?')">Delete</button>
                </form>
            </div>
        <?php endforeach; ?>
    </div>
</div>
