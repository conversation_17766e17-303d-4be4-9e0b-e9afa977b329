<?php
/**
 * Test page to verify CSRF and responsive fixes
 */

session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/Security.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$test_results = [];

// Test 1: CSRF Token Generation
try {
    $csrf_token = Security::generateCSRFToken();
    $test_results[] = [
        'test' => 'CSRF Token Generation',
        'result' => !empty($csrf_token) ? 'PASS' : 'FAIL',
        'details' => !empty($csrf_token) ? 'Token generated successfully' : 'Failed to generate token'
    ];
} catch (Exception $e) {
    $test_results[] = [
        'test' => 'CSRF Token Generation',
        'result' => 'FAIL',
        'details' => 'Error: ' . $e->getMessage()
    ];
}

// Test 2: CSRF Token Validation
try {
    $valid = Security::validateCSRFToken($csrf_token);
    $test_results[] = [
        'test' => 'CSRF Token Validation',
        'result' => $valid ? 'PASS' : 'FAIL',
        'details' => $valid ? 'Token validation successful' : 'Token validation failed'
    ];
} catch (Exception $e) {
    $test_results[] = [
        'test' => 'CSRF Token Validation',
        'result' => 'FAIL',
        'details' => 'Error: ' . $e->getMessage()
    ];
}

// Test 3: Check if responsive CSS exists
$responsive_css_exists = file_exists('../assets/css/responsive.css');
$test_results[] = [
    'test' => 'Responsive CSS File',
    'result' => $responsive_css_exists ? 'PASS' : 'FAIL',
    'details' => $responsive_css_exists ? 'File exists and accessible' : 'File not found'
];

// Test 4: Check section files for CSRF tokens
$sections_to_check = ['journey', 'advisors', 'publications', 'videos', 'gallery'];
$csrf_issues = [];

foreach ($sections_to_check as $section) {
    $file_path = "sections/{$section}.php";
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        $csrf_count = substr_count($content, 'csrf_token');
        if ($csrf_count < 3) { // Should have at least 3 (add, edit, delete)
            $csrf_issues[] = "{$section}.php has only {$csrf_count} CSRF tokens";
        }
    } else {
        $csrf_issues[] = "{$section}.php not found";
    }
}

$test_results[] = [
    'test' => 'Section Files CSRF Tokens',
    'result' => empty($csrf_issues) ? 'PASS' : 'WARNING',
    'details' => empty($csrf_issues) ? 'All sections have CSRF tokens' : implode(', ', $csrf_issues)
];

// Test 5: Check main pages for responsive CSS inclusion
$pages_to_check = ['../index.php', '../thesis-more.php', '../gallery-more.php', '../publications-more.php', '../advisors-more.php'];
$responsive_issues = [];

foreach ($pages_to_check as $page) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        if (strpos($content, 'assets/css/responsive.css') === false) {
            $responsive_issues[] = basename($page) . ' missing responsive CSS';
        }
    } else {
        $responsive_issues[] = basename($page) . ' not found';
    }
}

$test_results[] = [
    'test' => 'Pages Responsive CSS Inclusion',
    'result' => empty($responsive_issues) ? 'PASS' : 'WARNING',
    'details' => empty($responsive_issues) ? 'All pages include responsive CSS' : implode(', ', $responsive_issues)
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixes - CSRF & Responsive</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <style>
        .test-container {
            max-width: var(--container-lg);
            margin: 0 auto;
            padding: var(--spacing-lg);
        }
        
        .test-card {
            background: white;
            border-radius: clamp(0.5rem, 2vw, 1rem);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .responsive-demo {
            background: linear-gradient(45deg, #007bff, #6610f2);
            color: white;
            padding: var(--spacing-xl);
            border-radius: clamp(0.5rem, 2vw, 1rem);
            text-align: center;
            margin: var(--spacing-lg) 0;
        }
        
        .demo-text {
            font-size: var(--font-size-2xl);
            margin-bottom: var(--spacing-md);
        }
        
        .demo-subtitle {
            font-size: var(--font-size-base);
            opacity: 0.9;
        }
        
        .csrf-demo {
            background: #f8f9fa;
            padding: var(--spacing-md);
            border-radius: clamp(0.25rem, 1vw, 0.5rem);
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body class="bg-light">
    <div class="test-container">
        <div class="test-card">
            <h1>🔧 Fix Verification Results</h1>
            <p>This page tests the CSRF token and responsive design fixes.</p>
        </div>
        
        <!-- Test Results -->
        <div class="test-card">
            <h2>🧪 Test Results</h2>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Test</th>
                            <th>Result</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($test_results as $test): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($test['test']); ?></td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $test['result'] === 'PASS' ? 'success' : 
                                             ($test['result'] === 'WARNING' ? 'warning' : 'danger'); 
                                    ?>">
                                        <?php echo $test['result']; ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($test['details']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- CSRF Demo -->
        <div class="test-card">
            <h2>🔒 CSRF Token Demo</h2>
            <div class="csrf-demo">
                <p><strong>Current CSRF Token:</strong></p>
                <code><?php echo htmlspecialchars($csrf_token); ?></code>
                <p class="mt-3"><small>This token is now included in all admin forms to prevent CSRF attacks.</small></p>
            </div>
        </div>
        
        <!-- Responsive Demo -->
        <div class="test-card">
            <h2>📱 Responsive Design Demo</h2>
            <p>The elements below use viewport units (vw, vh) and clamp() for better responsiveness:</p>
            
            <div class="responsive-demo">
                <div class="demo-text">Responsive Text</div>
                <div class="demo-subtitle">This text scales with screen size using clamp()</div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="test-card">
                        <h5>Card 1</h5>
                        <p>Responsive padding and margins using CSS custom properties.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="test-card">
                        <h5>Card 2</h5>
                        <p>Font sizes scale smoothly across different screen sizes.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="test-card">
                        <h5>Card 3</h5>
                        <p>Border radius and spacing adapt to viewport size.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="test-card">
            <h2>🔗 Quick Links</h2>
            <div class="d-flex gap-3 flex-wrap">
                <a href="array_manager.php?section=journey" class="btn btn-primary">Test Journey Manager</a>
                <a href="array_manager.php?section=gallery" class="btn btn-primary">Test Gallery Manager</a>
                <a href="../index.php" class="btn btn-success">View Main Site</a>
                <a href="dashboard_new.php" class="btn btn-secondary">Back to Dashboard</a>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="test-card">
            <h2>📋 What Was Fixed</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>🔒 CSRF Token Issues</h5>
                    <ul>
                        <li>Added CSRF tokens to all admin forms</li>
                        <li>Fixed journey section edit/delete forms</li>
                        <li>Added validation in array_manager.php</li>
                        <li>Updated all section files</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>📱 Responsive Design</h5>
                    <ul>
                        <li>Created responsive.css with viewport units</li>
                        <li>Added clamp() for fluid typography</li>
                        <li>Improved mobile layouts</li>
                        <li>Added to all main pages</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
