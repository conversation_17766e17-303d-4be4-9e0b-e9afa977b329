<?php
// Check existing database structure

$host = 'localhost';
$username = 'root';  // Change this to your database username
$password = '';      // Change this to your database password
$dbname = 'dr_jd_2.0';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>Database: dr_jd_2.0</h3>";
    echo "<div class='alert alert-success'>Successfully connected to dr_jd_2.0 database!</div>";
    
    // Get all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h4>Existing Tables:</h4>";
    echo "<ul class='list-group'>";
    foreach ($tables as $table) {
        echo "<li class='list-group-item'>$table</li>";
    }
    echo "</ul>";
    
    // Check if admin tables already exist
    $admin_tables = ['admin_users', 'admin_sessions'];
    $existing_admin_tables = array_intersect($admin_tables, $tables);
    
    if (empty($existing_admin_tables)) {
        echo "<div class='alert alert-warning mt-3'>";
        echo "<strong>Admin tables not found.</strong> You need to run the setup script to create admin tables.";
        echo "<br><a href='setup_database.php' class='btn btn-primary mt-2'>Run Setup</a>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info mt-3'>";
        echo "<strong>Admin tables found:</strong> " . implode(', ', $existing_admin_tables);
        echo "<br><a href='dashboard_new.php' class='btn btn-success mt-2'>Go to Dashboard</a>";
        echo "</div>";
    }
    
    // Check visitor counter table if it exists
    if (in_array('visitor_counter', $tables) || in_array('visitors', $tables) || in_array('page_views', $tables)) {
        echo "<div class='alert alert-success mt-3'>";
        echo "<strong>Visitor counter table found!</strong> Your existing visitor counter will continue to work.";
        echo "</div>";
    }
    
} catch(PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<strong>Database connection failed:</strong> " . $e->getMessage();
    echo "<br><br><strong>Please check:</strong>";
    echo "<ul>";
    echo "<li>MySQL server is running</li>";
    echo "<li>Database 'dr_jd_2.0' exists</li>";
    echo "<li>Username and password are correct</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Check - dr_jd_2.0</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Database Structure Check</h4>
                    </div>
                    <div class="card-body">
                        <!-- Results will be displayed above -->
                        
                        <hr>
                        <div class="d-flex gap-2">
                            <a href="setup_database.php" class="btn btn-primary">Setup Admin Tables</a>
                            <a href="login.php" class="btn btn-secondary">Go to Login</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
