<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/Security.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Configure secure session
Security::configureSecureSession();

$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

$action = $_POST['action'] ?? '';
$success_message = '';
$error_message = '';

// Ensure website_settings structure exists
if (!isset($data['website_settings'])) {
    $data['website_settings'] = [];
}
if (!isset($data['website_settings']['seo'])) {
    $data['website_settings']['seo'] = [
        'title' => 'Dr. Jayanta Debbarma - Portfolio',
        'description' => 'Portfolio website of Dr. Jayanta Debbarma',
        'keywords' => ['portfolio', 'academic', 'research'],
        'author' => 'Dr. Jayanta Debbarma',
        'robots' => 'index, follow',
        'canonical_url' => '',
        'og_image' => '',
        'twitter_card' => 'summary_large_image',
        'google_analytics' => '',
        'google_search_console' => '',
        'schema_markup' => true
    ];
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF protection
    if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = "Invalid CSRF token. Please try again.";
    } else {
        switch ($action) {
            case 'update_seo':
                $seo_data = [
                    'title' => Security::sanitizeInput($_POST['title']),
                    'description' => Security::sanitizeInput($_POST['description']),
                    'keywords' => array_map('trim', explode(',', Security::sanitizeInput($_POST['keywords']))),
                    'author' => Security::sanitizeInput($_POST['author']),
                    'robots' => Security::sanitizeInput($_POST['robots']),
                    'canonical_url' => Security::validateURL($_POST['canonical_url']) ? $_POST['canonical_url'] : '',
                    'og_image' => Security::sanitizeInput($_POST['og_image']),
                    'twitter_card' => Security::sanitizeInput($_POST['twitter_card']),
                    'google_analytics' => Security::sanitizeInput($_POST['google_analytics']),
                    'google_search_console' => Security::sanitizeInput($_POST['google_search_console']),
                    'schema_markup' => isset($_POST['schema_markup'])
                ];
                
                $data['website_settings']['seo'] = $seo_data;
                $success_message = "SEO settings updated successfully!";
                break;
                
            case 'update_social_meta':
                $social_meta = [
                    'og_title' => Security::sanitizeInput($_POST['og_title']),
                    'og_description' => Security::sanitizeInput($_POST['og_description']),
                    'og_image' => Security::sanitizeInput($_POST['og_image']),
                    'og_url' => Security::validateURL($_POST['og_url']) ? $_POST['og_url'] : '',
                    'twitter_title' => Security::sanitizeInput($_POST['twitter_title']),
                    'twitter_description' => Security::sanitizeInput($_POST['twitter_description']),
                    'twitter_image' => Security::sanitizeInput($_POST['twitter_image']),
                    'twitter_site' => Security::sanitizeInput($_POST['twitter_site'])
                ];
                
                $data['website_settings']['social_meta'] = $social_meta;
                $success_message = "Social media meta tags updated successfully!";
                break;
                
            case 'update_analytics':
                $analytics = [
                    'google_analytics_id' => Security::sanitizeInput($_POST['google_analytics_id']),
                    'google_tag_manager_id' => Security::sanitizeInput($_POST['google_tag_manager_id']),
                    'facebook_pixel_id' => Security::sanitizeInput($_POST['facebook_pixel_id']),
                    'google_search_console_verification' => Security::sanitizeInput($_POST['google_search_console_verification']),
                    'bing_verification' => Security::sanitizeInput($_POST['bing_verification'])
                ];
                
                $data['website_settings']['analytics'] = $analytics;
                $success_message = "Analytics settings updated successfully!";
                break;
        }
        
        // Save updated data
        if ($success_message) {
            // Create backup
            $backup_file = '../data/backups/site-data-' . date('Y-m-d-H-i-s') . '.json';
            if (!is_dir('../data/backups')) {
                mkdir('../data/backups', 0755, true);
            }
            copy($json_file, $backup_file);
            
            // Save new data
            file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
            
            // Log the change
            Security::logSecurityEvent('SEO_SETTINGS_UPDATED', ['action' => $action]);
        }
    }
}

// Get current SEO data
$seo = $data['website_settings']['seo'] ?? [];
$social_meta = $data['website_settings']['social_meta'] ?? [];
$analytics = $data['website_settings']['analytics'] ?? [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Management</title>
    <link rel="icon" type="image/png" href="../assets/icons/fabicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/icons/fabicon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard_new.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <h4 class="mb-0">SEO Management</h4>
            </div>
        </nav>
        
        <div class="container mt-4">
            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- SEO Tabs -->
            <ul class="nav nav-tabs" id="seoTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="basic-seo-tab" data-bs-toggle="tab" data-bs-target="#basic-seo" type="button">
                        <i class="fas fa-search"></i> Basic SEO
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="social-meta-tab" data-bs-toggle="tab" data-bs-target="#social-meta" type="button">
                        <i class="fas fa-share-alt"></i> Social Media
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button">
                        <i class="fas fa-chart-line"></i> Analytics
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="preview-tab" data-bs-toggle="tab" data-bs-target="#preview" type="button">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="seoTabContent">
                <!-- Basic SEO Tab -->
                <div class="tab-pane fade show active" id="basic-seo" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-search"></i> Basic SEO Settings</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_seo">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Page Title</label>
                                            <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($seo['title'] ?? ''); ?>" maxlength="60">
                                            <div class="form-text">Recommended: 50-60 characters</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="author" class="form-label">Author</label>
                                            <input type="text" class="form-control" id="author" name="author" value="<?php echo htmlspecialchars($seo['author'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Meta Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" maxlength="160"><?php echo htmlspecialchars($seo['description'] ?? ''); ?></textarea>
                                    <div class="form-text">Recommended: 150-160 characters</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="keywords" class="form-label">Keywords (comma separated)</label>
                                            <input type="text" class="form-control" id="keywords" name="keywords" value="<?php echo htmlspecialchars(implode(', ', $seo['keywords'] ?? [])); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="robots" class="form-label">Robots</label>
                                            <select class="form-select" id="robots" name="robots">
                                                <option value="index, follow" <?php echo ($seo['robots'] ?? '') === 'index, follow' ? 'selected' : ''; ?>>Index, Follow</option>
                                                <option value="index, nofollow" <?php echo ($seo['robots'] ?? '') === 'index, nofollow' ? 'selected' : ''; ?>>Index, No Follow</option>
                                                <option value="noindex, follow" <?php echo ($seo['robots'] ?? '') === 'noindex, follow' ? 'selected' : ''; ?>>No Index, Follow</option>
                                                <option value="noindex, nofollow" <?php echo ($seo['robots'] ?? '') === 'noindex, nofollow' ? 'selected' : ''; ?>>No Index, No Follow</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="canonical_url" class="form-label">Canonical URL</label>
                                            <input type="url" class="form-control" id="canonical_url" name="canonical_url" value="<?php echo htmlspecialchars($seo['canonical_url'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="og_image" class="form-label">Default OG Image</label>
                                            <input type="text" class="form-control" id="og_image" name="og_image" value="<?php echo htmlspecialchars($seo['og_image'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="twitter_card" class="form-label">Twitter Card Type</label>
                                            <select class="form-select" id="twitter_card" name="twitter_card">
                                                <option value="summary" <?php echo ($seo['twitter_card'] ?? '') === 'summary' ? 'selected' : ''; ?>>Summary</option>
                                                <option value="summary_large_image" <?php echo ($seo['twitter_card'] ?? '') === 'summary_large_image' ? 'selected' : ''; ?>>Summary Large Image</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="schema_markup" name="schema_markup" <?php echo ($seo['schema_markup'] ?? false) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="schema_markup">
                                                    Enable Schema Markup
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update SEO Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media Tab -->
                <div class="tab-pane fade" id="social-meta" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-share-alt"></i> Social Media Meta Tags</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_social_meta">
                                
                                <h6>Open Graph (Facebook)</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="og_title" class="form-label">OG Title</label>
                                            <input type="text" class="form-control" id="og_title" name="og_title" value="<?php echo htmlspecialchars($social_meta['og_title'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="og_url" class="form-label">OG URL</label>
                                            <input type="url" class="form-control" id="og_url" name="og_url" value="<?php echo htmlspecialchars($social_meta['og_url'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="og_description" class="form-label">OG Description</label>
                                    <textarea class="form-control" id="og_description" name="og_description" rows="3"><?php echo htmlspecialchars($social_meta['og_description'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="og_image" class="form-label">OG Image</label>
                                    <input type="text" class="form-control" id="og_image" name="og_image" value="<?php echo htmlspecialchars($social_meta['og_image'] ?? ''); ?>">
                                    <div class="form-text">Recommended size: 1200x630 pixels</div>
                                </div>
                                
                                <hr>
                                
                                <h6>Twitter</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="twitter_title" class="form-label">Twitter Title</label>
                                            <input type="text" class="form-control" id="twitter_title" name="twitter_title" value="<?php echo htmlspecialchars($social_meta['twitter_title'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="twitter_site" class="form-label">Twitter Site (@username)</label>
                                            <input type="text" class="form-control" id="twitter_site" name="twitter_site" value="<?php echo htmlspecialchars($social_meta['twitter_site'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twitter_description" class="form-label">Twitter Description</label>
                                    <textarea class="form-control" id="twitter_description" name="twitter_description" rows="3"><?php echo htmlspecialchars($social_meta['twitter_description'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twitter_image" class="form-label">Twitter Image</label>
                                    <input type="text" class="form-control" id="twitter_image" name="twitter_image" value="<?php echo htmlspecialchars($social_meta['twitter_image'] ?? ''); ?>">
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Social Meta Tags
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Analytics Tab -->
                <div class="tab-pane fade" id="analytics" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> Analytics & Tracking</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_analytics">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                            <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id" value="<?php echo htmlspecialchars($analytics['google_analytics_id'] ?? ''); ?>" placeholder="G-XXXXXXXXXX">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="google_tag_manager_id" class="form-label">Google Tag Manager ID</label>
                                            <input type="text" class="form-control" id="google_tag_manager_id" name="google_tag_manager_id" value="<?php echo htmlspecialchars($analytics['google_tag_manager_id'] ?? ''); ?>" placeholder="GTM-XXXXXXX">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="facebook_pixel_id" class="form-label">Facebook Pixel ID</label>
                                            <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id" value="<?php echo htmlspecialchars($analytics['facebook_pixel_id'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="google_search_console_verification" class="form-label">Google Search Console Verification</label>
                                            <input type="text" class="form-control" id="google_search_console_verification" name="google_search_console_verification" value="<?php echo htmlspecialchars($analytics['google_search_console_verification'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="bing_verification" class="form-label">Bing Webmaster Verification</label>
                                    <input type="text" class="form-control" id="bing_verification" name="bing_verification" value="<?php echo htmlspecialchars($analytics['bing_verification'] ?? ''); ?>">
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Analytics Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Preview Tab -->
                <div class="tab-pane fade" id="preview" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-eye"></i> SEO Preview</h5>
                        </div>
                        <div class="card-body">
                            <h6>Google Search Result Preview</h6>
                            <div class="border p-3 mb-4" style="background-color: #f8f9fa;">
                                <div style="color: #1a0dab; font-size: 18px; text-decoration: underline; cursor: pointer;">
                                    <?php echo htmlspecialchars($seo['title'] ?? 'Your Page Title'); ?>
                                </div>
                                <div style="color: #006621; font-size: 14px;">
                                    <?php echo htmlspecialchars($seo['canonical_url'] ?? 'https://yourwebsite.com'); ?>
                                </div>
                                <div style="color: #545454; font-size: 13px; line-height: 1.4;">
                                    <?php echo htmlspecialchars($seo['description'] ?? 'Your meta description will appear here...'); ?>
                                </div>
                            </div>
                            
                            <h6>Facebook Share Preview</h6>
                            <div class="border p-3 mb-4" style="background-color: #f8f9fa; max-width: 500px;">
                                <?php if (!empty($social_meta['og_image'])): ?>
                                    <div style="background-color: #ddd; height: 200px; display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                        <span>OG Image Preview</span>
                                    </div>
                                <?php endif; ?>
                                <div style="font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($social_meta['og_title'] ?? $seo['title'] ?? 'Your Page Title'); ?>
                                </div>
                                <div style="color: #606770; font-size: 14px; margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($social_meta['og_description'] ?? $seo['description'] ?? 'Your description...'); ?>
                                </div>
                                <div style="color: #606770; font-size: 12px; text-transform: uppercase;">
                                    <?php echo htmlspecialchars(parse_url($social_meta['og_url'] ?? $seo['canonical_url'] ?? '', PHP_URL_HOST) ?? 'yourwebsite.com'); ?>
                                </div>
                            </div>
                            
                            <h6>Twitter Card Preview</h6>
                            <div class="border p-3" style="background-color: #f8f9fa; max-width: 500px;">
                                <?php if (!empty($social_meta['twitter_image'])): ?>
                                    <div style="background-color: #ddd; height: 200px; display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                        <span>Twitter Image Preview</span>
                                    </div>
                                <?php endif; ?>
                                <div style="font-weight: bold; font-size: 15px; margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($social_meta['twitter_title'] ?? $seo['title'] ?? 'Your Page Title'); ?>
                                </div>
                                <div style="color: #536471; font-size: 14px; margin-bottom: 5px;">
                                    <?php echo htmlspecialchars($social_meta['twitter_description'] ?? $seo['description'] ?? 'Your description...'); ?>
                                </div>
                                <div style="color: #536471; font-size: 13px;">
                                    <?php echo htmlspecialchars(parse_url($social_meta['og_url'] ?? $seo['canonical_url'] ?? '', PHP_URL_HOST) ?? 'yourwebsite.com'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Character count for title and description
        document.getElementById('title').addEventListener('input', function() {
            const length = this.value.length;
            const color = length > 60 ? 'red' : length > 50 ? 'orange' : 'green';
            this.style.borderColor = color;
        });
        
        document.getElementById('description').addEventListener('input', function() {
            const length = this.value.length;
            const color = length > 160 ? 'red' : length > 150 ? 'orange' : 'green';
            this.style.borderColor = color;
        });
    </script>
</body>
</html>
