<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$db_host = 'localhost';
$db_name = 'dr_jd_2.0';
$db_user = 'root';
$db_pass = '';

$debug_info = [];
$debug_info['timestamp'] = date('Y-m-d H:i:s');

try {
    // Create database connection
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    $debug_info['connection'] = 'success';

    // Set charset
    $conn->set_charset("utf8mb4");

    // Get visitor information with fallbacks
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

    $debug_info['ip_address'] = $ip_address;
    $debug_info['user_agent'] = substr($user_agent, 0, 50) . '...';

    // Handle proxy/forwarded IPs
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
        $ip_address = $_SERVER['HTTP_X_REAL_IP'];
    }

    // Check if tables exist
    $tables_check = $conn->query("SHOW TABLES LIKE 'visitor_counter'");
    if ($tables_check->num_rows == 0) {
        throw new Exception("visitor_counter table does not exist");
    }
    
    $tables_check = $conn->query("SHOW TABLES LIKE 'visitor_stats'");
    if ($tables_check->num_rows == 0) {
        throw new Exception("visitor_stats table does not exist");
    }

    $debug_info['tables'] = 'exist';

    // Start transaction for data consistency
    $conn->begin_transaction();

    try {
        // Record every visit (no uniqueness check)
        $insert_visit = "INSERT INTO visitor_counter (ip_address, user_agent) VALUES (?, ?)";
        $stmt = $conn->prepare($insert_visit);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        $stmt->bind_param("ss", $ip_address, $user_agent);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }
        
        $debug_info['insert'] = 'success';
        $stmt->close();

        // Check if visitor_stats has any records
        $check_stats = $conn->query("SELECT COUNT(*) as count FROM visitor_stats");
        $stats_count = $check_stats->fetch_assoc();
        
        if ($stats_count['count'] == 0) {
            // Create initial stats record
            $create_stats = "INSERT INTO visitor_stats (total_visits) VALUES (1)";
            if (!$conn->query($create_stats)) {
                throw new Exception("Create stats failed: " . $conn->error);
            }
            $debug_info['stats_created'] = true;
        } else {
            // Increment total visits count by 1
            $update_stats = "UPDATE visitor_stats SET 
                            total_visits = total_visits + 1,
                            last_updated = NOW()";
            
            if (!$conn->query($update_stats)) {
                throw new Exception("Update failed: " . $conn->error);
            }
            $debug_info['stats_updated'] = true;
        }

        // Get current total visits
        $get_stats = "SELECT total_visits FROM visitor_stats LIMIT 1";
        $result = $conn->query($get_stats);
        
        if (!$result) {
            throw new Exception("Query failed: " . $conn->error);
        }
        
        $stats = $result->fetch_assoc();
        $debug_info['stats_retrieved'] = $stats ? 'success' : 'no_data';

        // Commit transaction
        $conn->commit();

        // Return JSON response with just total visits
        echo json_encode([
            'success' => true,
            'total_visits' => (int)($stats['total_visits'] ?? 0),
            'debug' => $debug_info // Remove this line in production
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    // Log error
    error_log('Visitor counter error: ' . $e->getMessage());
    
    // Return error response with debug info
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'total_visits' => 0,
        'debug' => $debug_info // Remove this line in production
    ]);
} finally {
    // Close connection if it exists
    if (isset($conn) && $conn instanceof mysqli) {
        $conn->close();
    }
}
?>