<h3>Journey Management</h3>

<!-- Add New Journey Item Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5>Add New Journey Item</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="action" value="add_journey">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="year" class="form-label">Year/Period</label>
                        <input type="text" class="form-control" id="year" name="year" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
            </div>
            <button type="submit" class="btn btn-success">Add Journey Item</button>
        </form>
    </div>
</div>

<!-- Existing Journey Items -->
<div class="card">
    <div class="card-header">
        <h5>Existing Journey Items</h5>
    </div>
    <div class="card-body">
        <?php foreach ($data['journey'] as $index => $journey): ?>
            <div class="border p-3 mb-3">
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="edit_journey">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Year/Period</label>
                                <input type="text" class="form-control" name="year" value="<?php echo htmlspecialchars($journey['year']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($journey['title']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="4" required><?php echo htmlspecialchars($journey['description']); ?></textarea>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm">Update</button>
                    </div>
                </form>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="delete_journey">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this journey item?')">Delete</button>
                </form>
            </div>
        <?php endforeach; ?>
    </div>
</div>
