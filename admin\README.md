# Site Data Dashboard

This dashboard allows you to edit the JSON data file (`data/site-data.json`) through a user-friendly web interface with secure database-based authentication.

## 🔐 Security Features

- **Database-based Authentication**: Secure login system with MySQL database
- **Password Hashing**: Passwords are securely hashed using <PERSON><PERSON>'s password_hash()
- **Session Management**: Secure session handling with database-stored tokens
- **Profile Management**: Change password and update profile information
- **Session Expiry**: Automatic session cleanup and expiry handling

## 📋 Features

### Simple Edit Sections
- **Personal Information**: Edit name, email, title, bio, etc.
- **Address**: Edit address details
- **Contact**: Edit contact information
- **Statistics**: Edit numerical statistics
- **Social Links**: Edit social media links
- **Thesis**: Edit thesis information

### Array Management Sections (Add/Edit/Delete)
- **Education**: Manage education records
- **Publications**: Manage publications
- **Advisors**: Manage advisor information
- **Gallery**: Manage gallery items
- **Videos**: Manage YouTube videos
- **Journey**: Manage career journey timeline

### Admin Features
- **Profile Management**: Update name and email
- **Password Change**: Secure password change functionality
- **Session Tracking**: View last login time
- **Automatic Backups**: JSON backups created before each save

## 🚀 Setup Instructions

### 1. Database Setup (Using Existing dr_jd_2.0 Database)
1. **Check Database**: Navigate to `/admin/check_database.php` to verify your existing database
2. **Configure Database**: Edit `admin/config/database.php` with your MySQL credentials (already set to use dr_jd_2.0)
3. **Run Setup**: Navigate to `/admin/setup_database.php` to add admin tables to your existing database
4. **Note**: This will NOT affect your existing visitor counter or other data

### 2. Default Credentials
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **⚠️ IMPORTANT**: Change these credentials immediately after first login!

### 3. Access the Dashboard
1. Navigate to `/admin/` in your browser
2. Login with the default credentials
3. Change your password in Profile Settings

## 📖 How to Use

1. **Login**
   - Use the secure login form
   - Sessions are automatically managed

2. **Profile Management**
   - Click the user dropdown in the sidebar
   - Update your profile information
   - Change your password securely

3. **Simple Editing**
   - Click on any section in the sidebar
   - Edit the form fields
   - Click "Update" to save changes

4. **Array Management**
   - Click on sections like Education, Publications, etc.
   - You'll be redirected to the array manager
   - Use "Add New" forms to create new items
   - Edit existing items inline
   - Delete items with confirmation

## File Structure

```
admin/
├── config/
│   └── database.php              # Database configuration (dr_jd_2.0)
├── classes/
│   └── Auth.php                  # Authentication class
├── sections/
│   ├── dashboard_content.php     # Main dashboard sections
│   ├── education.php             # Education management
│   ├── publications.php          # Publications management
│   ├── advisors.php              # Advisors management
│   ├── gallery.php               # Gallery management
│   ├── videos.php                # Videos management
│   └── journey.php               # Journey management
├── check_database.php            # Check existing database structure
├── setup_database.php            # Add admin tables to dr_jd_2.0
├── login.php                     # Secure login page
├── dashboard_new.php             # New secure dashboard
├── array_manager.php             # Array data manager (updated)
├── ajax_handlers.php             # AJAX handlers for profile/password
├── index.php                     # Auto-redirect to login
└── README.md                     # Updated documentation
```

## Customization

- Modify form fields in section files
- Add new sections by creating new files in `sections/`
- Customize styling by modifying Bootstrap classes
- Add validation by enhancing the PHP code

## Troubleshooting

- Ensure the `data/site-data.json` file is writable
- Check PHP error logs if forms don't submit
- Verify file paths are correct for images and documents
- Make sure all required fields are filled when adding new items
