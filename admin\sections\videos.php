<h3>YouTube Videos Management</h3>

<!-- Add New Video Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5>Add New YouTube Video</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="action" value="add_video">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="youtube_id" class="form-label">YouTube Video ID</label>
                        <input type="text" class="form-control" id="youtube_id" name="youtube_id" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration (e.g., 15:30)</label>
                        <input type="text" class="form-control" id="duration" name="duration" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-control" id="category" name="category" required>
                            <option value="research">Research</option>
                            <option value="education">Education</option>
                            <option value="technical">Technical</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
            </div>
            <div class="mb-3">
                <label for="thumbnail" class="form-label">Thumbnail Image Path</label>
                <input type="text" class="form-control" id="thumbnail" name="thumbnail" required>
            </div>
            <button type="submit" class="btn btn-success">Add Video</button>
        </form>
    </div>
</div>

<!-- Existing Videos -->
<div class="card">
    <div class="card-header">
        <h5>Existing YouTube Videos</h5>
    </div>
    <div class="card-body">
        <?php foreach ($data['youtube_videos'] as $index => $video): ?>
            <div class="border p-3 mb-3">
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="edit_video">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($video['title']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">YouTube Video ID</label>
                                <input type="text" class="form-control" name="youtube_id" value="<?php echo htmlspecialchars($video['youtube_id']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Duration</label>
                                <input type="text" class="form-control" name="duration" value="<?php echo htmlspecialchars($video['duration']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-control" name="category" required>
                                    <option value="research" <?php echo $video['category'] === 'research' ? 'selected' : ''; ?>>Research</option>
                                    <option value="education" <?php echo $video['category'] === 'education' ? 'selected' : ''; ?>>Education</option>
                                    <option value="technical" <?php echo $video['category'] === 'technical' ? 'selected' : ''; ?>>Technical</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3" required><?php echo htmlspecialchars($video['description']); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Thumbnail Image Path</label>
                        <input type="text" class="form-control" name="thumbnail" value="<?php echo htmlspecialchars($video['thumbnail']); ?>" required>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm">Update</button>
                    </div>
                </form>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="delete_video">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this video?')">Delete</button>
                </form>
            </div>
        <?php endforeach; ?>
    </div>
</div>
