<?php
class Auth {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function login($email, $password) {
        try {
            $stmt = $this->pdo->prepare("SELECT id, email, password_hash, name, is_active FROM admin_users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user && $user['is_active'] && password_verify($password, $user['password_hash'])) {
                // Update last login
                $stmt = $this->pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                // Create session
                $session_token = bin2hex(random_bytes(32));
                $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
                
                $stmt = $this->pdo->prepare("INSERT INTO admin_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)");
                $stmt->execute([$user['id'], $session_token, $expires_at]);
                
                // Set session variables
                $_SESSION['admin_authenticated'] = true;
                $_SESSION['admin_user_id'] = $user['id'];
                $_SESSION['admin_email'] = $user['email'];
                $_SESSION['admin_name'] = $user['name'];
                $_SESSION['admin_session_token'] = $session_token;
                
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }
    
    public function logout() {
        if (isset($_SESSION['admin_session_token'])) {
            // Remove session from database
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE session_token = ?");
            $stmt->execute([$_SESSION['admin_session_token']]);
        }
        
        // Clear session
        session_destroy();
    }
    
    public function isAuthenticated() {
        if (!isset($_SESSION['admin_authenticated']) || !$_SESSION['admin_authenticated']) {
            return false;
        }
        
        if (!isset($_SESSION['admin_session_token'])) {
            return false;
        }
        
        // Check if session is valid in database
        $stmt = $this->pdo->prepare("SELECT user_id FROM admin_sessions WHERE session_token = ? AND expires_at > NOW()");
        $stmt->execute([$_SESSION['admin_session_token']]);
        
        return $stmt->fetch() !== false;
    }
    
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        $stmt = $this->pdo->prepare("SELECT id, email, name, last_login FROM admin_users WHERE id = ?");
        $stmt->execute([$_SESSION['admin_user_id']]);
        
        return $stmt->fetch();
    }
    
    public function changePassword($current_password, $new_password) {
        if (!$this->isAuthenticated()) {
            return ['success' => false, 'message' => 'Not authenticated'];
        }
        
        try {
            // Verify current password
            $stmt = $this->pdo->prepare("SELECT password_hash FROM admin_users WHERE id = ?");
            $stmt->execute([$_SESSION['admin_user_id']]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($current_password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Validate new password
            if (strlen($new_password) < 6) {
                return ['success' => false, 'message' => 'New password must be at least 6 characters long'];
            }
            
            // Update password
            $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("UPDATE admin_users SET password_hash = ? WHERE id = ?");
            $stmt->execute([$new_password_hash, $_SESSION['admin_user_id']]);
            
            // Invalidate all existing sessions except current one
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE user_id = ? AND session_token != ?");
            $stmt->execute([$_SESSION['admin_user_id'], $_SESSION['admin_session_token']]);
            
            return ['success' => true, 'message' => 'Password changed successfully'];
            
        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while changing password'];
        }
    }
    
    public function cleanExpiredSessions() {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM admin_sessions WHERE expires_at < NOW()");
            $stmt->execute();
        } catch (Exception $e) {
            error_log("Session cleanup error: " . $e->getMessage());
        }
    }
    
    public function updateProfile($name, $email) {
        if (!$this->isAuthenticated()) {
            return ['success' => false, 'message' => 'Not authenticated'];
        }
        
        try {
            // Check if email is already taken by another user
            $stmt = $this->pdo->prepare("SELECT id FROM admin_users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $_SESSION['admin_user_id']]);
            
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'Email is already taken'];
            }
            
            // Update profile
            $stmt = $this->pdo->prepare("UPDATE admin_users SET name = ?, email = ? WHERE id = ?");
            $stmt->execute([$name, $email, $_SESSION['admin_user_id']]);
            
            // Update session variables
            $_SESSION['admin_name'] = $name;
            $_SESSION['admin_email'] = $email;
            
            return ['success' => true, 'message' => 'Profile updated successfully'];
            
        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while updating profile'];
        }
    }
}
?>
