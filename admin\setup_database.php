<?php
// Database setup script - Run this once to create admin tables in existing dr_jd_2.0 database

$host = 'localhost';
$username = 'root';  // Change this to your database username
$password = '';      // Change this to your database password
$dbname = 'dr_jd_2.0';  // Using your existing database

try {
    // Connect to your existing database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create admin_users table
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE
    )";
    
    $pdo->exec($sql);
    
    // Create default admin user
    $default_email = '<EMAIL>';
    $default_password = 'admin123';
    $default_name = 'Administrator';
    
    // Check if admin user already exists
    $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE email = ?");
    $stmt->execute([$default_email]);
    
    if (!$stmt->fetch()) {
        // Create default admin user
        $password_hash = password_hash($default_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admin_users (email, password_hash, name) VALUES (?, ?, ?)");
        $stmt->execute([$default_email, $password_hash, $default_name]);
        
        echo "<div class='alert alert-success'>Admin tables and user created successfully in dr_jd_2.0 database!</div>";
        echo "<div class='alert alert-info'>";
        echo "<strong>Default Login Credentials:</strong><br>";
        echo "Email: $default_email<br>";
        echo "Password: $default_password<br>";
        echo "<strong>Please change these credentials after first login!</strong>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>Admin tables and user already exist in dr_jd_2.0 database.</div>";
    }
    
    // Create login sessions table
    $sql = "CREATE TABLE IF NOT EXISTS admin_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
    )";
    
    $pdo->exec($sql);
    
    echo "<div class='alert alert-success'>Admin setup completed successfully in dr_jd_2.0 database!</div>";
    echo "<div class='alert alert-info'><a href='dashboard_new.php' class='btn btn-primary'>Go to Dashboard</a></div>";
    
} catch(PDOException $e) {
    echo "<div class='alert alert-danger'>Admin setup failed: " . $e->getMessage() . "</div>";
    echo "<div class='alert alert-warning'>Please make sure MySQL is running, the dr_jd_2.0 database exists, and the credentials are correct.</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Admin Setup for dr_jd_2.0 Database</h4>
                    </div>
                    <div class="card-body">
                        <p>This script creates admin tables in your existing <strong>dr_jd_2.0</strong> database and creates a default admin user.</p>
                        <div class="alert alert-info">
                            <strong>Note:</strong> This will add admin tables to your existing dr_jd_2.0 database without affecting your visitor counter or other existing data.
                        </div>
                        <hr>
                        <!-- Results will be displayed above -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
