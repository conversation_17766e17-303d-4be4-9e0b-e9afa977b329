<?php
// Setup visitor counter tables
header('Content-Type: application/json');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once 'admin/config/database.php';

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'status' => 'starting',
    'steps' => []
];

try {
    // Create database connection using the config
    $conn = new mysqli($host, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    $response['steps'][] = "✅ Connected to database: $dbname";
    
    // Set charset
    $conn->set_charset("utf8mb4");
    
    // Check if visitor_counter table exists
    $result = $conn->query("SHOW TABLES LIKE 'visitor_counter'");
    if ($result->num_rows == 0) {
        // Create visitor_counter table
        $sql = "CREATE TABLE visitor_counter (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ip (ip_address),
            INDEX idx_time (visit_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql)) {
            $response['steps'][] = "✅ Created visitor_counter table";
        } else {
            throw new Exception("Error creating visitor_counter table: " . $conn->error);
        }
    } else {
        $response['steps'][] = "ℹ️ visitor_counter table already exists";
    }
    
    // Check if visitor_stats table exists
    $result = $conn->query("SHOW TABLES LIKE 'visitor_stats'");
    if ($result->num_rows == 0) {
        // Create visitor_stats table
        $sql = "CREATE TABLE visitor_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            total_visits INT DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql)) {
            $response['steps'][] = "✅ Created visitor_stats table";
            
            // Insert initial record
            $conn->query("INSERT INTO visitor_stats (total_visits) VALUES (0)");
            $response['steps'][] = "✅ Initialized visitor stats with 0 visits";
        } else {
            throw new Exception("Error creating visitor_stats table: " . $conn->error);
        }
    } else {
        $response['steps'][] = "ℹ️ visitor_stats table already exists";
    }
    
    // Verify tables and get current stats
    $result = $conn->query("SELECT total_visits FROM visitor_stats LIMIT 1");
    if ($result && $row = $result->fetch_assoc()) {
        $response['current_visits'] = (int)$row['total_visits'];
        $response['steps'][] = "📊 Current total visits: " . $response['current_visits'];
    }
    
    // Get table info
    $tables_info = [];
    
    // visitor_counter info
    $result = $conn->query("SELECT COUNT(*) as count FROM visitor_counter");
    if ($result && $row = $result->fetch_assoc()) {
        $tables_info['visitor_counter_records'] = (int)$row['count'];
    }
    
    // visitor_stats info
    $result = $conn->query("SELECT COUNT(*) as count FROM visitor_stats");
    if ($result && $row = $result->fetch_assoc()) {
        $tables_info['visitor_stats_records'] = (int)$row['count'];
    }
    
    $response['tables_info'] = $tables_info;
    $response['status'] = 'success';
    $response['message'] = 'Visitor counter tables are ready!';
    
    $conn->close();
    
} catch (Exception $e) {
    $response['status'] = 'error';
    $response['error'] = $e->getMessage();
    $response['steps'][] = "❌ Error: " . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
