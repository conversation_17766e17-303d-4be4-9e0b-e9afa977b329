<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

$section = $_GET['section'] ?? 'education';
$action = $_POST['action'] ?? '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'add_education':
            $new_item = [
                'title' => $_POST['title'],
                'institution' => $_POST['institution'],
                'year' => $_POST['year']
            ];
            $data['education'][] = $new_item;
            break;
            
        case 'edit_education':
            $index = (int)$_POST['index'];
            $data['education'][$index] = [
                'title' => $_POST['title'],
                'institution' => $_POST['institution'],
                'year' => $_POST['year']
            ];
            break;
            
        case 'delete_education':
            $index = (int)$_POST['index'];
            array_splice($data['education'], $index, 1);
            break;
            
        case 'add_publication':
            $max_id = 0;
            foreach ($data['publications'] as $pub) {
                if ($pub['id'] > $max_id) $max_id = $pub['id'];
            }
            $new_item = [
                'id' => $max_id + 1,
                'title' => $_POST['title'],
                'journal' => $_POST['journal'],
                'year' => (int)$_POST['year'],
                'authors' => explode(',', $_POST['authors']),
                'abstract' => $_POST['abstract'],
                'pdf_file' => $_POST['pdf_file'],
                'link' => $_POST['link']
            ];
            $data['publications'][] = $new_item;
            break;
            
        case 'edit_publication':
            $index = (int)$_POST['index'];
            $data['publications'][$index] = [
                'id' => $data['publications'][$index]['id'],
                'title' => $_POST['title'],
                'journal' => $_POST['journal'],
                'year' => (int)$_POST['year'],
                'authors' => explode(',', $_POST['authors']),
                'abstract' => $_POST['abstract'],
                'pdf_file' => $_POST['pdf_file'],
                'link' => $_POST['link']
            ];
            break;
            
        case 'delete_publication':
            $index = (int)$_POST['index'];
            array_splice($data['publications'], $index, 1);
            break;
            
        case 'add_advisor':
            $max_id = 0;
            foreach ($data['advisors'] as $advisor) {
                if ($advisor['id'] > $max_id) $max_id = $advisor['id'];
            }
            $new_item = [
                'id' => $max_id + 1,
                'name' => $_POST['name'],
                'title' => $_POST['title'],
                'institution' => $_POST['institution'],
                'image' => $_POST['image'],
                'bio' => $_POST['bio'],
                'specialization' => $_POST['specialization'],
                'role' => $_POST['role']
            ];
            $data['advisors'][] = $new_item;
            break;
            
        case 'edit_advisor':
            $index = (int)$_POST['index'];
            $data['advisors'][$index] = [
                'id' => $data['advisors'][$index]['id'],
                'name' => $_POST['name'],
                'title' => $_POST['title'],
                'institution' => $_POST['institution'],
                'image' => $_POST['image'],
                'bio' => $_POST['bio'],
                'specialization' => $_POST['specialization'],
                'role' => $_POST['role']
            ];
            break;
            
        case 'delete_advisor':
            $index = (int)$_POST['index'];
            array_splice($data['advisors'], $index, 1);
            break;
            
        case 'add_gallery':
            $max_id = 0;
            foreach ($data['gallery'] as $item) {
                if ($item['id'] > $max_id) $max_id = $item['id'];
            }
            $new_item = [
                'id' => $max_id + 1,
                'title' => $_POST['title'],
                'category' => $_POST['category'],
                'type' => $_POST['type'],
                'image' => $_POST['image'],
                'description' => $_POST['description'],
                'link' => $_POST['link']
            ];
            $data['gallery'][] = $new_item;
            break;
            
        case 'edit_gallery':
            $index = (int)$_POST['index'];
            $data['gallery'][$index] = [
                'id' => $data['gallery'][$index]['id'],
                'title' => $_POST['title'],
                'category' => $_POST['category'],
                'type' => $_POST['type'],
                'image' => $_POST['image'],
                'description' => $_POST['description'],
                'link' => $_POST['link']
            ];
            break;
            
        case 'delete_gallery':
            $index = (int)$_POST['index'];
            array_splice($data['gallery'], $index, 1);
            break;
            
        case 'add_video':
            $max_id = 0;
            foreach ($data['youtube_videos'] as $video) {
                if ($video['id'] > $max_id) $max_id = $video['id'];
            }
            $new_item = [
                'id' => $max_id + 1,
                'title' => $_POST['title'],
                'description' => $_POST['description'],
                'youtube_id' => $_POST['youtube_id'],
                'thumbnail' => $_POST['thumbnail'],
                'duration' => $_POST['duration'],
                'category' => $_POST['category']
            ];
            $data['youtube_videos'][] = $new_item;
            break;
            
        case 'edit_video':
            $index = (int)$_POST['index'];
            $data['youtube_videos'][$index] = [
                'id' => $data['youtube_videos'][$index]['id'],
                'title' => $_POST['title'],
                'description' => $_POST['description'],
                'youtube_id' => $_POST['youtube_id'],
                'thumbnail' => $_POST['thumbnail'],
                'duration' => $_POST['duration'],
                'category' => $_POST['category']
            ];
            break;
            
        case 'delete_video':
            $index = (int)$_POST['index'];
            array_splice($data['youtube_videos'], $index, 1);
            break;
            
        case 'add_journey':
            $new_item = [
                'year' => $_POST['year'],
                'title' => $_POST['title'],
                'description' => $_POST['description']
            ];
            $data['journey'][] = $new_item;
            break;
            
        case 'edit_journey':
            $index = (int)$_POST['index'];
            $data['journey'][$index] = [
                'year' => $_POST['year'],
                'title' => $_POST['title'],
                'description' => $_POST['description']
            ];
            break;
            
        case 'delete_journey':
            $index = (int)$_POST['index'];
            array_splice($data['journey'], $index, 1);
            break;
    }
    
    // Save updated data
    file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
    $success_message = "Data updated successfully!";
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Array Data Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-light bg-light">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="dashboard_new.php">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <div class="navbar-nav">
                            <a class="nav-link <?php echo $section === 'education' ? 'active' : ''; ?>" href="?section=education">Education</a>
                            <a class="nav-link <?php echo $section === 'publications' ? 'active' : ''; ?>" href="?section=publications">Publications</a>
                            <a class="nav-link <?php echo $section === 'advisors' ? 'active' : ''; ?>" href="?section=advisors">Advisors</a>
                            <a class="nav-link <?php echo $section === 'gallery' ? 'active' : ''; ?>" href="?section=gallery">Gallery</a>
                            <a class="nav-link <?php echo $section === 'videos' ? 'active' : ''; ?>" href="?section=videos">Videos</a>
                            <a class="nav-link <?php echo $section === 'journey' ? 'active' : ''; ?>" href="?section=journey">Journey</a>
                        </div>
                    </div>
                </nav>
                
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success mt-3"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <div class="container mt-4">
                    <?php
                    switch ($section) {
                        case 'education':
                            include 'sections/education.php';
                            break;
                        case 'publications':
                            include 'sections/publications.php';
                            break;
                        case 'advisors':
                            include 'sections/advisors.php';
                            break;
                        case 'gallery':
                            include 'sections/gallery.php';
                            break;
                        case 'videos':
                            include 'sections/videos.php';
                            break;
                        case 'journey':
                            include 'sections/journey.php';
                            break;
                        default:
                            echo '<h3>Section not found</h3>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
