<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

$action = $_POST['action'] ?? '';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'add_publication':
            $max_id = 0;
            foreach ($data['publications'] as $pub) {
                if ($pub['id'] > $max_id) $max_id = $pub['id'];
            }
            $new_item = [
                'id' => $max_id + 1,
                'title' => $_POST['title'],
                'journal' => $_POST['journal'],
                'year' => (int)$_POST['year'],
                'authors' => array_map('trim', explode(',', $_POST['authors'])),
                'abstract' => $_POST['abstract'],
                'pdf_file' => $_POST['pdf_file'],
                'link' => $_POST['link']
            ];
            $data['publications'][] = $new_item;
            $success_message = "Publication added successfully!";
            break;
            
        case 'edit_publication':
            $index = (int)$_POST['index'];
            if (isset($data['publications'][$index])) {
                $data['publications'][$index] = [
                    'id' => $data['publications'][$index]['id'],
                    'title' => $_POST['title'],
                    'journal' => $_POST['journal'],
                    'year' => (int)$_POST['year'],
                    'authors' => array_map('trim', explode(',', $_POST['authors'])),
                    'abstract' => $_POST['abstract'],
                    'pdf_file' => $_POST['pdf_file'],
                    'link' => $_POST['link']
                ];
                $success_message = "Publication updated successfully!";
            } else {
                $error_message = "Publication not found!";
            }
            break;
            
        case 'delete_publication':
            $index = (int)$_POST['index'];
            if (isset($data['publications'][$index])) {
                array_splice($data['publications'], $index, 1);
                $success_message = "Publication deleted successfully!";
            } else {
                $error_message = "Publication not found!";
            }
            break;
    }
    
    // Save updated data
    if ($success_message) {
        // Create backup
        $backup_file = '../data/backups/site-data-' . date('Y-m-d-H-i-s') . '.json';
        if (!is_dir('../data/backups')) {
            mkdir('../data/backups', 0755, true);
        }
        copy($json_file, $backup_file);
        
        // Save new data
        file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Publications Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-light bg-light">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="dashboard_new.php">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <h4 class="mb-0">Publications Management</h4>
                    </div>
                </nav>
                
                <div class="container mt-4">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <h3>Publications Management</h3>
                    <p class="text-muted">Total Publications: <?php echo count($data['publications']); ?></p>

                    <!-- Add New Publication Form -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-plus"></i> Add New Publication</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="add_publication">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title *</label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="journal" class="form-label">Journal *</label>
                                            <input type="text" class="form-control" id="journal" name="journal" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="year" class="form-label">Year *</label>
                                            <input type="number" class="form-control" id="year" name="year" min="1900" max="2030" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="authors" class="form-label">Authors (comma separated) *</label>
                                            <input type="text" class="form-control" id="authors" name="authors" placeholder="Dr. John Doe, Dr. Jane Smith" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="abstract" class="form-label">Abstract *</label>
                                    <textarea class="form-control" id="abstract" name="abstract" rows="4" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="pdf_file" class="form-label">PDF File Path</label>
                                            <input type="text" class="form-control" id="pdf_file" name="pdf_file" placeholder="assets/documents/publications/paper.pdf">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="link" class="form-label">External Link</label>
                                            <input type="url" class="form-control" id="link" name="link" placeholder="https://scholar.google.com/...">
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Publication
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Existing Publications -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Existing Publications (<?php echo count($data['publications']); ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($data['publications'])): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No publications found. Add your first publication using the form above.
                                </div>
                            <?php else: ?>
                                <?php foreach ($data['publications'] as $index => $publication): ?>
                                    <div class="border p-3 mb-3 rounded">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="text-primary">Publication #<?php echo $publication['id']; ?></h6>
                                            <span class="badge bg-secondary"><?php echo $publication['year']; ?></span>
                                        </div>
                                        
                                        <form method="POST" class="mb-3">
                                            <input type="hidden" name="action" value="edit_publication">
                                            <input type="hidden" name="index" value="<?php echo $index; ?>">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Title</label>
                                                        <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($publication['title']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Journal</label>
                                                        <input type="text" class="form-control" name="journal" value="<?php echo htmlspecialchars($publication['journal']); ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Year</label>
                                                        <input type="number" class="form-control" name="year" value="<?php echo $publication['year']; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Authors (comma separated)</label>
                                                        <input type="text" class="form-control" name="authors" value="<?php echo htmlspecialchars(implode(', ', $publication['authors'])); ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Abstract</label>
                                                <textarea class="form-control" name="abstract" rows="3" required><?php echo htmlspecialchars($publication['abstract']); ?></textarea>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">PDF File Path</label>
                                                        <input type="text" class="form-control" name="pdf_file" value="<?php echo htmlspecialchars($publication['pdf_file']); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">External Link</label>
                                                        <input type="url" class="form-control" name="link" value="<?php echo htmlspecialchars($publication['link']); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-save"></i> Update
                                                </button>
                                            </div>
                                        </form>
                                        
                                        <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this publication?')">
                                            <input type="hidden" name="action" value="delete_publication">
                                            <input type="hidden" name="index" value="<?php echo $index; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
