# Assets Folder Structure

This folder contains all the assets used in the Dr. Jayanta Debbarma portfolio website.

## Folder Structure

```
assets/
├── images/
│   ├── profile/          # Profile photos and headshots
│   ├── gallery/          # Gallery images for research work
│   ├── advisors/         # Photos of key advisors and mentors
│   ├── publications/     # Images related to publications
│   └── background/       # Background images and patterns
├── documents/
│   ├── thesis/           # PhD thesis and related documents
│   ├── publications/     # Research papers and publications (PDF)
│   ├── certificates/     # Awards and certificates
│   └── cv/              # CV and resume files
├── videos/              # Video presentations and demos
└── icons/               # Custom icons and logos
```

## File Naming Conventions

### Images
- Profile photos: `profile_main.jpg`, `profile_formal.jpg`, etc.
- Gallery images: `gallery_01.jpg`, `gallery_02.jpg`, etc.
- Advisor photos: `advisor_[name].jpg`
- Publication images: `pub_[year]_[title].jpg`

### Documents
- Thesis: `thesis_phd_[year].pdf`
- Publications: `pub_[year]_[journal]_[title].pdf`
- Certificates: `cert_[year]_[award].pdf`
- CV: `cv_[date].pdf`

### Videos
- Presentations: `presentation_[title]_[date].mp4`
- Demos: `demo_[project]_[date].mp4`

## Supported File Formats

### Images
- JPEG (.jpg, .jpeg) - For photos
- PNG (.png) - For images with transparency
- WebP (.webp) - For optimized web images
- SVG (.svg) - For vector graphics and icons

### Documents
- PDF (.pdf) - For all document types
- DOC/DOCX (.doc, .docx) - For editable documents

### Videos
- MP4 (.mp4) - Primary video format
- WebM (.webm) - Alternative web format
- MOV (.mov) - For high-quality videos

## Usage

All assets are referenced through the JSON configuration file (`data/site-data.json`) and loaded dynamically into the website. This allows for easy content management without modifying the HTML/CSS code.

## Image Optimization

For best performance:
- Compress images before uploading
- Use appropriate formats (JPEG for photos, PNG for graphics)
- Consider WebP format for modern browsers
- Maintain aspect ratios for consistent layouts

## Security

- Only upload trusted files
- Scan all documents for malware before uploading
- Keep sensitive documents in password-protected formats
- Regular backup of all assets
