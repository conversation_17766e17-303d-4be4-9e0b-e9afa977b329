<?php
// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $journey = $dataLoader->getJourney();
    $awards = $dataLoader->getAwards();
    $seo = $dataLoader->getMetaTags();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta Debbarma'];
    $journey = [];
    $awards = [];
    $seo = ['title' => 'Journey - Dr. Jayanta Debbarma'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?> - Professional Journey</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e67e22;
            --accent-light: #f39c12;
            --success-color: #27ae60;
            --text-color: #2c3e50;
            --text-light: #7f8c8d;
            --light-gray: #ecf0f1;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            --gradient-accent: linear-gradient(45deg, #e67e22, #f39c12);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--light-gray);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 100px 0 50px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .back-btn {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Main Content */
        .main-content {
            padding: 80px 0;
        }
        
        /* Timeline */
        .timeline-section {
            margin-bottom: 80px;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 50px;
        }
        
        .timeline {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--gradient-accent);
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 60px;
            width: 50%;
        }
        
        .timeline-item:nth-child(odd) {
            left: 0;
            padding-right: 40px;
            text-align: right;
        }
        
        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 40px;
        }
        
        .timeline-dot {
            position: absolute;
            width: 24px;
            height: 24px;
            background: var(--accent-color);
            border-radius: 50%;
            top: 20px;
            border: 4px solid var(--white);
            box-shadow: 0 0 0 4px var(--accent-color);
        }
        
        .timeline-item:nth-child(odd) .timeline-dot {
            right: -12px;
        }
        
        .timeline-item:nth-child(even) .timeline-dot {
            left: -12px;
        }
        
        .timeline-content {
            background: var(--white);
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }
        
        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }
        
        .timeline-year {
            color: var(--accent-color);
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        
        .timeline-content h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .timeline-content p {
            color: var(--text-light);
            line-height: 1.7;
        }
        
        /* Awards Section */
        .awards-section {
            background: var(--white);
            padding: 80px 0;
            border-radius: 20px;
            margin-bottom: 80px;
        }
        
        .awards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .award-card {
            background: var(--light-gray);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .award-card:hover {
            border-color: var(--accent-color);
            transform: translateY(-5px);
        }
        
        .award-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: var(--white);
        }
        
        .award-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
        
        .award-year {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .award-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .timeline::before {
                left: 20px;
            }
            
            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 50px !important;
                padding-right: 0 !important;
                text-align: left !important;
            }
            
            .timeline-dot {
                left: 10px !important;
                right: auto !important;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .back-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-flex;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Main Page
        </a>
        <div class="container">
            <h1>Professional Journey</h1>
            <p>Explore the milestones and achievements throughout my career</p>
            <div style="margin-top: 30px;">
                <a href="about-more.php" style="color: white; text-decoration: none; margin: 0 15px;">About</a>
                <a href="publications-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Publications</a>
                <a href="thesis-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Thesis</a>
                <a href="advisors-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Advisors</a>
                <a href="gallery-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Gallery</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Timeline Section -->
            <div class="timeline-section" data-aos="fade-up">
                <h2 class="section-title">Career Timeline</h2>
                <div class="timeline">
                    <?php if (!empty($journey)): ?>
                        <?php echo renderJourney($journey); ?>
                    <?php else: ?>
                        <!-- Default timeline items -->
                        <div class="timeline-item" data-aos="fade-right">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-year">1985-1989</div>
                                <h3>Bachelor's Degree in Mechanical Engineering</h3>
                                <p>University of California, Berkeley - Graduated with honors, focusing on fundamental engineering principles and design methodologies.</p>
                            </div>
                        </div>
                        <div class="timeline-item" data-aos="fade-left">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-year">1989-1991</div>
                                <h3>Master's Degree in Materials Science</h3>
                                <p>Stanford University - Specialized in advanced materials research with focus on sustainable engineering applications.</p>
                            </div>
                        </div>
                        <div class="timeline-item" data-aos="fade-right">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-year">1991-1995</div>
                                <h3>Ph.D. in Mechanical Engineering</h3>
                                <p>Massachusetts Institute of Technology (MIT) - Doctoral research on innovative engineering solutions and technology development.</p>
                            </div>
                        </div>
                        <div class="timeline-item" data-aos="fade-left">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-year">1995-2000</div>
                                <h3>Research Engineer</h3>
                                <p>General Electric Research Center - Led multiple research projects focusing on advanced materials and manufacturing processes.</p>
                            </div>
                        </div>
                        <div class="timeline-item" data-aos="fade-right">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-year">2000-2010</div>
                                <h3>Senior Research Scientist</h3>
                                <p>IBM Research Division - Managed interdisciplinary teams and developed breakthrough technologies in engineering applications.</p>
                            </div>
                        </div>
                        <div class="timeline-item" data-aos="fade-left">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-year">2010-Present</div>
                                <h3>Chief Technology Officer</h3>
                                <p>Tech Innovations Inc. - Leading strategic technology initiatives and driving innovation across multiple engineering disciplines.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Awards Section -->
            <div class="awards-section" data-aos="fade-up">
                <div class="container">
                    <h2 class="section-title">Awards & Recognition</h2>
                    <div class="awards-grid">
                        <?php if (!empty($awards)): ?>
                            <?php $delay = 0; ?>
                            <?php foreach ($awards as $award): ?>
                                <div class="award-card" data-aos="zoom-in" data-aos-delay="<?php echo $delay; ?>">
                                    <div class="award-icon">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                    <h3><?php echo escape($award['title']); ?></h3>
                                    <div class="award-year"><?php echo escape($award['year']); ?></div>
                                    <p class="award-description"><?php echo escape($award['description']); ?></p>
                                </div>
                                <?php $delay += 100; ?>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <!-- Default awards -->
                            <div class="award-card" data-aos="zoom-in">
                                <div class="award-icon">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <h3>Excellence in Engineering Award</h3>
                                <div class="award-year">2022</div>
                                <p class="award-description">Recognized for outstanding contributions to sustainable engineering practices and innovation in materials science.</p>
                            </div>
                            <div class="award-card" data-aos="zoom-in" data-aos-delay="100">
                                <div class="award-icon">
                                    <i class="fas fa-medal"></i>
                                </div>
                                <h3>Innovation Leadership Medal</h3>
                                <div class="award-year">2020</div>
                                <p class="award-description">Awarded for pioneering research in advanced materials and their applications in modern engineering.</p>
                            </div>
                            <div class="award-card" data-aos="zoom-in" data-aos-delay="200">
                                <div class="award-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <h3>Distinguished Researcher Award</h3>
                                <div class="award-year">2018</div>
                                <p class="award-description">Honored for exceptional research contributions and mentorship in the field of engineering and technology.</p>
                            </div>
                            <div class="award-card" data-aos="zoom-in" data-aos-delay="300">
                                <div class="award-icon">
                                    <i class="fas fa-certificate"></i>
                                </div>
                                <h3>Professional Excellence Certificate</h3>
                                <div class="award-year">2015</div>
                                <p class="award-description">Certified for maintaining the highest standards of professional practice and ethical conduct in engineering.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
