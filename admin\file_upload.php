<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// CSRF Token generation
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$success_message = '';
$error_message = '';

// File upload configuration
$upload_config = [
    'documents' => [
        'path' => '../assets/documents/',
        'allowed_types' => ['pdf', 'doc', 'docx'],
        'max_size' => 10 * 1024 * 1024, // 10MB
        'description' => 'Documents (PDF, DOC, DOCX) - Max 10MB'
    ],
    'images' => [
        'path' => '../assets/images/',
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'max_size' => 5 * 1024 * 1024, // 5MB
        'description' => 'Images (JPG, PNG, GIF, WEBP) - Max 5MB'
    ],
    'thesis' => [
        'path' => '../assets/documents/thesis/',
        'allowed_types' => ['pdf'],
        'max_size' => 50 * 1024 * 1024, // 50MB
        'description' => 'Thesis Files (PDF only) - Max 50MB'
    ],
    'publications' => [
        'path' => '../assets/documents/publications/',
        'allowed_types' => ['pdf'],
        'max_size' => 20 * 1024 * 1024, // 20MB
        'description' => 'Publication Files (PDF only) - Max 20MB'
    ]
];

// Create upload directories if they don't exist
foreach ($upload_config as $type => $config) {
    if (!is_dir($config['path'])) {
        mkdir($config['path'], 0755, true);
    }
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload'])) {
    // CSRF protection
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error_message = "Invalid CSRF token. Please try again.";
    } else {
        $upload_type = $_POST['upload_type'] ?? '';
        
        if (!isset($upload_config[$upload_type])) {
            $error_message = "Invalid upload type.";
        } elseif (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            $error_message = "File upload failed. Please try again.";
        } else {
            $file = $_FILES['file'];
            $config = $upload_config[$upload_type];
            
            // Validate file size
            if ($file['size'] > $config['max_size']) {
                $error_message = "File size exceeds maximum allowed size.";
            } else {
                // Validate file type
                $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                if (!in_array($file_extension, $config['allowed_types'])) {
                    $error_message = "File type not allowed. Allowed types: " . implode(', ', $config['allowed_types']);
                } else {
                    // Generate secure filename
                    $original_name = pathinfo($file['name'], PATHINFO_FILENAME);
                    $safe_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $original_name);
                    $timestamp = date('Y-m-d_H-i-s');
                    $new_filename = $safe_name . '_' . $timestamp . '.' . $file_extension;
                    $upload_path = $config['path'] . $new_filename;
                    
                    // Additional security checks
                    $finfo = finfo_open(FILEINFO_MIME_TYPE);
                    $mime_type = finfo_file($finfo, $file['tmp_name']);
                    finfo_close($finfo);
                    
                    $allowed_mimes = [
                        'pdf' => 'application/pdf',
                        'doc' => 'application/msword',
                        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'jpg' => 'image/jpeg',
                        'jpeg' => 'image/jpeg',
                        'png' => 'image/png',
                        'gif' => 'image/gif',
                        'webp' => 'image/webp'
                    ];
                    
                    if (!isset($allowed_mimes[$file_extension]) || $mime_type !== $allowed_mimes[$file_extension]) {
                        $error_message = "File type validation failed. File may be corrupted or not a valid " . strtoupper($file_extension) . " file.";
                    } else {
                        // Move uploaded file
                        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                            $relative_path = str_replace('../', '', $upload_path);
                            $success_message = "File uploaded successfully! Path: " . $relative_path;
                            
                            // Log upload activity
                            $user = $auth->getCurrentUser();
                            error_log("File uploaded by user {$user['email']}: {$relative_path}");
                        } else {
                            $error_message = "Failed to move uploaded file.";
                        }
                    }
                }
            }
        }
    }
}

// Get uploaded files for display
function getUploadedFiles($directory) {
    $files = [];
    if (is_dir($directory)) {
        $scan = scandir($directory);
        foreach ($scan as $file) {
            if ($file !== '.' && $file !== '..' && is_file($directory . $file)) {
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                $files[] = [
                    'name' => $file,
                    'size' => filesize($directory . $file),
                    'modified' => filemtime($directory . $file),
                    'path' => str_replace(['../', '\\'], ['', '/'], $directory . $file),
                    'extension' => $extension,
                    'is_image' => in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']),
                    'is_pdf' => $extension === 'pdf'
                ];
            }
        }
    }
    return $files;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Manager</title>
    <link rel="icon" type="image/png" href="../assets/icons/fabicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/icons/fabicon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .file-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .file-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .file-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .file-preview {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #dee2e6;
        }
        .file-icon {
            width: 60px;
            height: 60px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #6c757d;
        }
        .pdf-icon { background: #dc3545; color: white; }
        .doc-icon { background: #0d6efd; color: white; }
        .image-icon { background: #198754; color: white; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard_new.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <h4 class="mb-0">File Upload Manager</h4>
            </div>
        </nav>
        
        <div class="container mt-4">
            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-upload"></i> Upload Files</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                
                                <div class="mb-3">
                                    <label for="upload_type" class="form-label">Upload Category</label>
                                    <select class="form-select" id="upload_type" name="upload_type" required>
                                        <option value="">Select category...</option>
                                        <?php foreach ($upload_config as $type => $config): ?>
                                            <option value="<?php echo $type; ?>"><?php echo ucfirst($type); ?> - <?php echo $config['description']; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5>Drag & Drop Files Here</h5>
                                    <p class="text-muted">or click to browse</p>
                                    <input type="file" id="fileInput" name="file" class="d-none" required>
                                </div>
                                
                                <div id="fileInfo" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <strong>Selected File:</strong> <span id="fileName"></span><br>
                                        <strong>Size:</strong> <span id="fileSize"></span>
                                    </div>
                                </div>
                                
                                <button type="submit" name="upload" class="btn btn-primary mt-3">
                                    <i class="fas fa-upload"></i> Upload File
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-folder"></i> Uploaded Files</h5>
                        </div>
                        <div class="card-body">
                            <div class="accordion" id="filesAccordion">
                                <?php foreach ($upload_config as $type => $config): ?>
                                    <?php $files = getUploadedFiles($config['path']); ?>
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading<?php echo ucfirst($type); ?>">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo ucfirst($type); ?>">
                                                <?php echo ucfirst($type); ?> (<?php echo count($files); ?> files)
                                            </button>
                                        </h2>
                                        <div id="collapse<?php echo ucfirst($type); ?>" class="accordion-collapse collapse" data-bs-parent="#filesAccordion">
                                            <div class="accordion-body">
                                                <div class="file-list">
                                                    <?php if (empty($files)): ?>
                                                        <p class="text-muted">No files uploaded yet.</p>
                                                    <?php else: ?>
                                                        <?php foreach ($files as $file): ?>
                                                            <div class="file-item">
                                                                <div class="d-flex align-items-center gap-3">
                                                                    <!-- File Preview/Icon -->
                                                                    <div class="flex-shrink-0">
                                                                        <?php if ($file['is_image']): ?>
                                                                            <img src="<?php echo htmlspecialchars($file['path']); ?>"
                                                                                 alt="Preview"
                                                                                 class="file-preview"
                                                                                 onclick="showImageModal('<?php echo htmlspecialchars($file['path']); ?>', '<?php echo htmlspecialchars($file['name']); ?>')">
                                                                        <?php elseif ($file['is_pdf']): ?>
                                                                            <div class="file-icon pdf-icon">
                                                                                <i class="fas fa-file-pdf"></i>
                                                                            </div>
                                                                        <?php elseif (in_array($file['extension'], ['doc', 'docx'])): ?>
                                                                            <div class="file-icon doc-icon">
                                                                                <i class="fas fa-file-word"></i>
                                                                            </div>
                                                                        <?php else: ?>
                                                                            <div class="file-icon">
                                                                                <i class="fas fa-file"></i>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    </div>

                                                                    <!-- File Info -->
                                                                    <div class="flex-grow-1">
                                                                        <strong><?php echo htmlspecialchars($file['name']); ?></strong><br>
                                                                        <small class="text-muted">
                                                                            Size: <?php echo number_format($file['size'] / 1024, 2); ?> KB |
                                                                            Modified: <?php echo date('Y-m-d H:i', $file['modified']); ?>
                                                                        </small><br>
                                                                        <small class="text-primary">Path: <?php echo htmlspecialchars($file['path']); ?></small>
                                                                    </div>

                                                                    <!-- Actions -->
                                                                    <div class="flex-shrink-0">
                                                                        <div class="btn-group-vertical">
                                                                            <button class="btn btn-sm btn-outline-primary mb-1" onclick="copyPath('<?php echo htmlspecialchars($file['path']); ?>')">
                                                                                <i class="fas fa-copy"></i> Copy Path
                                                                            </button>
                                                                            <?php if ($file['is_image']): ?>
                                                                                <button class="btn btn-sm btn-outline-info mb-1" onclick="showImageModal('<?php echo htmlspecialchars($file['path']); ?>', '<?php echo htmlspecialchars($file['name']); ?>')">
                                                                                    <i class="fas fa-eye"></i> View
                                                                                </button>
                                                                            <?php endif; ?>
                                                                            <?php if ($file['is_pdf']): ?>
                                                                                <a href="<?php echo htmlspecialchars($file['path']); ?>" target="_blank" class="btn btn-sm btn-outline-success">
                                                                                    <i class="fas fa-external-link-alt"></i> Open
                                                                                </a>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Preview Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Preview" class="img-fluid" style="max-height: 70vh;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="copyModalImagePath()">
                        <i class="fas fa-copy"></i> Copy Path
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFileInfo(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFileInfo(e.target.files[0]);
            }
        });
        
        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = (file.size / 1024).toFixed(2) + ' KB';
            fileInfo.style.display = 'block';
        }
        
        // Copy path to clipboard
        function copyPath(path) {
            navigator.clipboard.writeText(path).then(function() {
                showToast('Path copied to clipboard!', 'success');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = path;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Path copied to clipboard!', 'success');
            });
        }

        // Show image in modal
        function showImageModal(imagePath, imageName) {
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('imageModalLabel');

            modalImage.src = imagePath;
            modalTitle.textContent = imageName;
            modalImage.dataset.path = imagePath;

            modal.show();
        }

        // Copy image path from modal
        function copyModalImagePath() {
            const imagePath = document.getElementById('modalImage').dataset.path;
            copyPath(imagePath);
        }

        // Show toast notification
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.top = '20px';
            toast.style.right = '20px';
            toast.style.zIndex = '9999';
            toast.style.minWidth = '250px';
            toast.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i> ${message}`;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
        
        // Form validation
        document.getElementById('uploadForm').addEventListener('submit', (e) => {
            const uploadType = document.getElementById('upload_type').value;
            const file = fileInput.files[0];
            
            if (!uploadType) {
                e.preventDefault();
                alert('Please select an upload category.');
                return;
            }
            
            if (!file) {
                e.preventDefault();
                alert('Please select a file to upload.');
                return;
            }
        });
    </script>
</body>
</html>
