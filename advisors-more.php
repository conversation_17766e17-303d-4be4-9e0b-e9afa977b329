<?php
// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $advisors = $dataLoader->getAdvisors();
    $seo = $dataLoader->getMetaTags();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta De<PERSON>'];
    $advisors = [];
    $seo = ['title' => 'Advisors - Dr. <PERSON>'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?> - Key Advisors</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Responsive CSS -->
    <link rel="stylesheet" href="assets/css/responsive.css">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e67e22;
            --accent-light: #f39c12;
            --success-color: #27ae60;
            --text-color: #2c3e50;
            --text-light: #7f8c8d;
            --light-gray: #ecf0f1;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            --gradient-accent: linear-gradient(45deg, #e67e22, #f39c12);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--light-gray);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 100px 0 50px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .back-btn {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Main Content */
        .main-content {
            padding: 80px 0;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 50px;
        }
        
        /* Advisors Grid */
        .advisors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-bottom: 80px;
        }
        
        .advisor-card {
            background: var(--white);
            border-radius: 20px;
            padding: 40px;
            box-shadow: var(--shadow-light);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .advisor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--gradient-accent);
        }
        
        .advisor-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-medium);
        }
        
        .advisor-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 25px;
            overflow: hidden;
            border: 5px solid var(--light-gray);
            transition: all 0.3s ease;
        }
        
        .advisor-card:hover .advisor-image {
            border-color: var(--accent-color);
        }
        
        .advisor-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .advisor-name {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .advisor-title {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        
        .advisor-institution {
            color: var(--text-light);
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .advisor-bio {
            color: var(--text-color);
            line-height: 1.7;
            margin-bottom: 25px;
        }
        
        .advisor-specialties {
            margin-bottom: 25px;
        }
        
        .advisor-specialties h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .specialty-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }
        
        .specialty-tag {
            background: var(--light-gray);
            color: var(--text-color);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }
        
        .specialty-tag:hover {
            background: var(--accent-color);
            color: var(--white);
        }
        
        .advisor-contact {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .contact-link {
            width: 40px;
            height: 40px;
            background: var(--light-gray);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .contact-link:hover {
            background: var(--accent-color);
            color: var(--white);
            transform: translateY(-2px);
        }
        
        /* Collaboration Section */
        .collaboration-section {
            background: var(--white);
            padding: 80px 0;
            border-radius: 20px;
            margin-bottom: 50px;
        }
        
        .collaboration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .collaboration-card {
            background: var(--light-gray);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            border-left: 5px solid var(--accent-color);
        }
        
        .collaboration-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: var(--white);
        }
        
        .collaboration-card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .collaboration-card p {
            color: var(--text-light);
            line-height: 1.6;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .advisors-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .back-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-flex;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Main Page
        </a>
        <div class="container">
            <h1>Key Advisors & Mentors</h1>
            <p>Distinguished professionals who have guided my research and career development</p>
            <div style="margin-top: 30px;">
                <a href="about-more.php" style="color: white; text-decoration: none; margin: 0 15px;">About</a>
                <a href="journey-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Journey</a>
                <a href="publications-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Publications</a>
                <a href="thesis-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Thesis</a>
                <a href="gallery-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Gallery</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Advisors Grid -->
            <div class="advisors-section" data-aos="fade-up">
                <h2 class="section-title">Academic & Professional Advisors</h2>
                <div class="advisors-grid">
                    <?php if (!empty($advisors)): ?>
                        <?php $delay = 0; ?>
                        <?php foreach ($advisors as $advisor): ?>
                            <div class="advisor-card" data-aos="zoom-in" data-aos-delay="<?php echo $delay; ?>">
                                <div class="advisor-image">
                                    <?php if (!empty($advisor['image']) && file_exists($advisor['image'])): ?>
                                        <img src="<?php echo escape($advisor['image']); ?>" alt="<?php echo escape($advisor['name']); ?>">
                                    <?php else: ?>
                                        <img src="https://picsum.photos/seed/<?php echo urlencode($advisor['name']); ?>/300/300.jpg" alt="<?php echo escape($advisor['name']); ?>">
                                    <?php endif; ?>
                                </div>
                                <h3 class="advisor-name"><?php echo escape($advisor['name']); ?></h3>
                                <div class="advisor-title"><?php echo escape($advisor['title']); ?></div>
                                <?php if (!empty($advisor['institution'])): ?>
                                    <div class="advisor-institution"><?php echo escape($advisor['institution']); ?></div>
                                <?php endif; ?>
                                <p class="advisor-bio"><?php echo escape($advisor['bio']); ?></p>
                                <?php if (!empty($advisor['specialties'])): ?>
                                    <div class="advisor-specialties">
                                        <h4>Areas of Expertise</h4>
                                        <div class="specialty-tags">
                                            <?php foreach ($advisor['specialties'] as $specialty): ?>
                                                <span class="specialty-tag"><?php echo escape($specialty); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="advisor-contact">
                                    <a href="#" class="contact-link" title="Email">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                    <a href="#" class="contact-link" title="LinkedIn">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                    <a href="#" class="contact-link" title="Research Profile">
                                        <i class="fas fa-graduation-cap"></i>
                                    </a>
                                </div>
                            </div>
                            <?php $delay += 100; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Default advisors -->
                        <div class="advisor-card" data-aos="zoom-in">
                            <div class="advisor-image">
                                <img src="https://picsum.photos/seed/advisor1/300/300.jpg" alt="Dr. Emily Johnson">
                            </div>
                            <h3 class="advisor-name">Dr. Emily Johnson</h3>
                            <div class="advisor-title">Professor of Materials Science</div>
                            <div class="advisor-institution">Massachusetts Institute of Technology</div>
                            <p class="advisor-bio">Renowned expert in nanomaterials with over 25 years of research experience. Advisor on advanced materials projects and sustainable engineering solutions.</p>
                            <div class="advisor-specialties">
                                <h4>Areas of Expertise</h4>
                                <div class="specialty-tags">
                                    <span class="specialty-tag">Nanomaterials</span>
                                    <span class="specialty-tag">Materials Science</span>
                                    <span class="specialty-tag">Sustainable Engineering</span>
                                </div>
                            </div>
                            <div class="advisor-contact">
                                <a href="#" class="contact-link" title="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                                <a href="#" class="contact-link" title="LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" class="contact-link" title="Research Profile">
                                    <i class="fas fa-graduation-cap"></i>
                                </a>
                            </div>
                        </div>

                        <div class="advisor-card" data-aos="zoom-in" data-aos-delay="100">
                            <div class="advisor-image">
                                <img src="https://picsum.photos/seed/advisor2/300/300.jpg" alt="Dr. Robert Williams">
                            </div>
                            <h3 class="advisor-name">Dr. Robert Williams</h3>
                            <div class="advisor-title">Director of Engineering Research</div>
                            <div class="advisor-institution">Stanford University</div>
                            <p class="advisor-bio">Specialist in sustainable engineering solutions and renewable energy systems. Key advisor on environmental projects and green technology development.</p>
                            <div class="advisor-specialties">
                                <h4>Areas of Expertise</h4>
                                <div class="specialty-tags">
                                    <span class="specialty-tag">Renewable Energy</span>
                                    <span class="specialty-tag">Environmental Engineering</span>
                                    <span class="specialty-tag">Green Technology</span>
                                </div>
                            </div>
                            <div class="advisor-contact">
                                <a href="#" class="contact-link" title="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                                <a href="#" class="contact-link" title="LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" class="contact-link" title="Research Profile">
                                    <i class="fas fa-graduation-cap"></i>
                                </a>
                            </div>
                        </div>

                        <div class="advisor-card" data-aos="zoom-in" data-aos-delay="200">
                            <div class="advisor-image">
                                <img src="https://picsum.photos/seed/advisor3/300/300.jpg" alt="Dr. Sarah Davis">
                            </div>
                            <h3 class="advisor-name">Dr. Sarah Davis</h3>
                            <div class="advisor-title">Chief Technology Officer</div>
                            <div class="advisor-institution">Tech Innovations Inc.</div>
                            <p class="advisor-bio">Expert in robotics and automation systems with extensive industry experience. Advisor on technology integration projects and innovation management.</p>
                            <div class="advisor-specialties">
                                <h4>Areas of Expertise</h4>
                                <div class="specialty-tags">
                                    <span class="specialty-tag">Robotics</span>
                                    <span class="specialty-tag">Automation</span>
                                    <span class="specialty-tag">Technology Integration</span>
                                </div>
                            </div>
                            <div class="advisor-contact">
                                <a href="#" class="contact-link" title="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                                <a href="#" class="contact-link" title="LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" class="contact-link" title="Research Profile">
                                    <i class="fas fa-graduation-cap"></i>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Collaboration Section -->
            <div class="collaboration-section" data-aos="fade-up">
                <div class="container">
                    <h2 class="section-title">Collaborative Approach</h2>
                    <div class="collaboration-grid">
                        <div class="collaboration-card" data-aos="zoom-in">
                            <div class="collaboration-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <h3>Mentorship</h3>
                            <p>Long-term relationships built on mutual respect, continuous learning, and shared commitment to advancing engineering knowledge.</p>
                        </div>
                        <div class="collaboration-card" data-aos="zoom-in" data-aos-delay="100">
                            <div class="collaboration-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h3>Innovation</h3>
                            <p>Collaborative research initiatives that push the boundaries of traditional engineering and explore new technological frontiers.</p>
                        </div>
                        <div class="collaboration-card" data-aos="zoom-in" data-aos-delay="200">
                            <div class="collaboration-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h3>Global Impact</h3>
                            <p>Working together to address global challenges through sustainable engineering solutions and responsible technology development.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
