<?php
// Prevent any output before JSO<PERSON>
ob_start();

session_start();

// Include required files
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Security.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Clear any output and set JSON header
ob_clean();
header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        exit;
    }
    
    // CSRF protection
    if (!isset($input['csrf_token']) || !Security::validateCSRFToken($input['csrf_token'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
    
    $about_image = $input['about_image'] ?? '';
    
    if (empty($about_image)) {
        echo json_encode(['success' => false, 'message' => 'No image path provided']);
        exit;
    }
    
    // Load current data
    $json_file = '../../data/site-data.json';
    if (!file_exists($json_file)) {
        echo json_encode(['success' => false, 'message' => 'JSON file not found']);
        exit;
    }
    
    $data = json_decode(file_get_contents($json_file), true);
    if (!$data) {
        echo json_encode(['success' => false, 'message' => 'Failed to parse JSON file']);
        exit;
    }
    
    // Update about_image
    $data['personal']['about_image'] = Security::sanitizeInput($about_image);
    
    // Create backup
    $backup_dir = '../../data/backups';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . '/site-data-' . date('Y-m-d-H-i-s') . '.json';
    copy($json_file, $backup_file);
    
    // Save updated data
    $result = file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
    
    if ($result === false) {
        echo json_encode(['success' => false, 'message' => 'Failed to save JSON file']);
        exit;
    }
    
    // Verify the save
    $saved_data = json_decode(file_get_contents($json_file), true);
    $saved_about_image = $saved_data['personal']['about_image'] ?? '';
    
    if ($saved_about_image === $about_image) {
        echo json_encode([
            'success' => true, 
            'message' => 'About image updated successfully',
            'about_image' => $about_image,
            'backup_file' => basename($backup_file)
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Verification failed - data not saved correctly']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
exit;
?>
