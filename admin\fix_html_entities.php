<?php
/**
 * Fix HTML entities in site data
 * Run this once to clean up any &#039; or other HTML entities in your data
 */

session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';

// Function to recursively decode HTML entities
function decodeHtmlEntities($data) {
    if (is_array($data)) {
        return array_map('decodeHtmlEntities', $data);
    } elseif (is_string($data)) {
        // Decode HTML entities back to normal characters
        return html_entity_decode($data, ENT_QUOTES, 'UTF-8');
    }
    return $data;
}

// Function to clean and normalize text
function cleanText($text) {
    if (!is_string($text)) return $text;
    
    // First decode any HTML entities
    $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
    
    // Remove any remaining problematic characters
    $text = str_replace(['&#039;', '&quot;', '&amp;'], ["'", '"', '&'], $text);
    
    // Trim whitespace
    $text = trim($text);
    
    return $text;
}

try {
    // Load current data
    if (!file_exists($json_file)) {
        throw new Exception("JSON data file not found: " . $json_file);
    }
    
    $jsonContent = file_get_contents($json_file);
    $data = json_decode($jsonContent, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Error parsing JSON: " . json_last_error_msg());
    }
    
    $changes_made = false;
    $report = [];
    
    // Clean gallery data
    if (isset($data['gallery']) && is_array($data['gallery'])) {
        foreach ($data['gallery'] as $index => &$item) {
            $original_title = $item['title'] ?? '';
            $original_description = $item['description'] ?? '';
            
            $item['title'] = cleanText($item['title'] ?? '');
            $item['description'] = cleanText($item['description'] ?? '');
            $item['image'] = cleanText($item['image'] ?? '');
            $item['link'] = cleanText($item['link'] ?? '');
            $item['category'] = cleanText($item['category'] ?? '');
            $item['type'] = cleanText($item['type'] ?? '');
            
            if ($original_title !== $item['title'] || $original_description !== $item['description']) {
                $changes_made = true;
                $report[] = "Gallery item {$index}: Fixed HTML entities";
            }
        }
    }
    
    // Clean other sections that might have HTML entities
    $sections_to_clean = ['education', 'publications', 'advisors', 'journey', 'youtube_videos'];
    
    foreach ($sections_to_clean as $section) {
        if (isset($data[$section]) && is_array($data[$section])) {
            foreach ($data[$section] as $index => &$item) {
                if (is_array($item)) {
                    foreach ($item as $key => &$value) {
                        if (is_string($value)) {
                            $original_value = $value;
                            $value = cleanText($value);
                            if ($original_value !== $value) {
                                $changes_made = true;
                                $report[] = "Section {$section}, item {$index}, field {$key}: Fixed HTML entities";
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Clean personal information
    if (isset($data['personal']) && is_array($data['personal'])) {
        foreach ($data['personal'] as $key => &$value) {
            if (is_string($value)) {
                $original_value = $value;
                $value = cleanText($value);
                if ($original_value !== $value) {
                    $changes_made = true;
                    $report[] = "Personal info, field {$key}: Fixed HTML entities";
                }
            }
        }
    }
    
    if ($changes_made) {
        // Create backup
        $backup_file = '../data/site-data-backup-' . date('Y-m-d-H-i-s') . '.json';
        file_put_contents($backup_file, $jsonContent);
        
        // Save cleaned data
        file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $success_message = "HTML entities fixed successfully! Backup created: " . basename($backup_file);
    } else {
        $success_message = "No HTML entities found that needed fixing.";
    }
    
} catch (Exception $e) {
    $error_message = "Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix HTML Entities</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Fix HTML Entities in Site Data</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($success_message)): ?>
                            <div class="alert alert-success">
                                <?php echo $success_message; ?>
                            </div>
                            
                            <?php if (!empty($report)): ?>
                                <div class="alert alert-info">
                                    <h6>Changes Made:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($report as $change): ?>
                                            <li><?php echo htmlspecialchars($change); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="array_manager.php?section=gallery" class="btn btn-primary">Go to Gallery Manager</a>
                                <a href="dashboard_new.php" class="btn btn-secondary">Back to Dashboard</a>
                            </div>
                            
                        <?php elseif (isset($error_message)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error_message; ?>
                            </div>
                            <a href="dashboard_new.php" class="btn btn-secondary">Back to Dashboard</a>
                            
                        <?php else: ?>
                            <p>This script will scan your site data and fix any HTML entities like <code>&#039;</code> that should be normal characters.</p>
                            <div class="alert alert-warning">
                                <strong>Note:</strong> A backup will be created before making any changes.
                            </div>
                            <form method="POST">
                                <button type="submit" class="btn btn-primary">Fix HTML Entities</button>
                                <a href="dashboard_new.php" class="btn btn-secondary">Cancel</a>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
