<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';

// Check if file exists
if (!file_exists($json_file)) {
    $error = "JSON file not found: $json_file";
} else {
    // Try to load and decode JSON
    $json_content = file_get_contents($json_file);
    $data = json_decode($json_content, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $error = "JSON decode error: " . json_last_error_msg();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard_new.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <h4 class="mb-0">Data Test</h4>
            </div>
        </nav>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <h5>Error Loading Data</h5>
                <p><?php echo htmlspecialchars($error); ?></p>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <h5>JSON Data Loaded Successfully!</h5>
                <p>File: <?php echo $json_file; ?></p>
                <p>File size: <?php echo number_format(filesize($json_file)); ?> bytes</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Data Summary</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Publications</span>
                                    <span class="badge bg-primary"><?php echo count($data['publications'] ?? []); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Education Records</span>
                                    <span class="badge bg-primary"><?php echo count($data['education'] ?? []); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Advisors</span>
                                    <span class="badge bg-primary"><?php echo count($data['advisors'] ?? []); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Gallery Items</span>
                                    <span class="badge bg-primary"><?php echo count($data['gallery'] ?? []); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>YouTube Videos</span>
                                    <span class="badge bg-primary"><?php echo count($data['youtube_videos'] ?? []); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Journey Items</span>
                                    <span class="badge bg-primary"><?php echo count($data['journey'] ?? []); ?></span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Publications Preview</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($data['publications'])): ?>
                                <?php foreach (array_slice($data['publications'], 0, 3) as $pub): ?>
                                    <div class="border-bottom pb-2 mb-2">
                                        <h6><?php echo htmlspecialchars($pub['title']); ?></h6>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($pub['journal']); ?> (<?php echo $pub['year']; ?>)
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                                <?php if (count($data['publications']) > 3): ?>
                                    <small class="text-muted">... and <?php echo count($data['publications']) - 3; ?> more</small>
                                <?php endif; ?>
                            <?php else: ?>
                                <p class="text-muted">No publications found</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="publications_manager.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-book"></i> Manage Publications
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="education_manager.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-graduation-cap"></i> Manage Education
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="array_manager.php?section=advisors" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-users"></i> Manage Advisors
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="array_manager.php?section=gallery" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-images"></i> Manage Gallery
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
