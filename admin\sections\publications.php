<h3>Publications Management</h3>

<!-- Add New Publication Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5>Add New Publication</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="action" value="add_publication">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="journal" class="form-label">Journal</label>
                        <input type="text" class="form-control" id="journal" name="journal" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="year" class="form-label">Year</label>
                        <input type="number" class="form-control" id="year" name="year" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="authors" class="form-label">Authors (comma separated)</label>
                        <input type="text" class="form-control" id="authors" name="authors" required>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="abstract" class="form-label">Abstract</label>
                <textarea class="form-control" id="abstract" name="abstract" rows="3" required></textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="pdf_file" class="form-label">PDF File Path</label>
                        <input type="text" class="form-control" id="pdf_file" name="pdf_file">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="link" class="form-label">External Link</label>
                        <input type="url" class="form-control" id="link" name="link">
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success">Add Publication</button>
        </form>
    </div>
</div>

<!-- Existing Publications -->
<div class="card">
    <div class="card-header">
        <h5>Existing Publications</h5>
    </div>
    <div class="card-body">
        <?php foreach ($data['publications'] as $index => $publication): ?>
            <div class="border p-3 mb-3">
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="edit_publication">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($publication['title']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Journal</label>
                                <input type="text" class="form-control" name="journal" value="<?php echo htmlspecialchars($publication['journal']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Year</label>
                                <input type="number" class="form-control" name="year" value="<?php echo $publication['year']; ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Authors (comma separated)</label>
                                <input type="text" class="form-control" name="authors" value="<?php echo htmlspecialchars(implode(', ', $publication['authors'])); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Abstract</label>
                        <textarea class="form-control" name="abstract" rows="3" required><?php echo htmlspecialchars($publication['abstract']); ?></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">PDF File Path</label>
                                <input type="text" class="form-control" name="pdf_file" value="<?php echo htmlspecialchars($publication['pdf_file']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">External Link</label>
                                <input type="url" class="form-control" name="link" value="<?php echo htmlspecialchars($publication['link']); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm">Update</button>
                    </div>
                </form>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="delete_publication">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this publication?')">Delete</button>
                </form>
            </div>
        <?php endforeach; ?>
    </div>
</div>
