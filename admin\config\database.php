<?php
// Database configuration - Using existing dr_jd_2.0 database
$host = 'localhost';
$dbname = 'dr_jd_2.0';  // Using your existing database
$username = 'root';     // Change this to your database username
$password = '';         // Change this to your database password

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}
?>
