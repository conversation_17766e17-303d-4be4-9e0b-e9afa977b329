<?php
// Turn off error reporting to prevent HTML output
error_reporting(0);
ini_set('display_errors', 0);

// Prevent any output before JSON
ob_start();

session_start();

// Include required files
try {
    require_once '../config/database.php';
    require_once '../classes/Auth.php';
    require_once '../classes/Security.php';
} catch (Exception $e) {
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Configuration error: ' . $e->getMessage()]);
    exit;
}

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Clear any output and set JSON header
ob_clean();
header('Content-Type: application/json');

// Handle file upload manually to avoid widget issues
try {
    // CSRF protection
    if (!isset($_POST['csrf_token']) || !Security::validateCSRFToken($_POST['csrf_token'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }

    $category = $_POST['category'] ?? 'images';

    // Upload configuration - Fixed paths from admin/components/ perspective
    $upload_config = [
        'images' => [
            'path' => '../../assets/images/',
            'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'max_size' => 5 * 1024 * 1024, // 5MB
        ],
        'documents' => [
            'path' => '../../assets/documents/',
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'max_size' => 10 * 1024 * 1024, // 10MB
        ],
        'gallery' => [
            'path' => '../../assets/images/gallery/',
            'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'max_size' => 5 * 1024 * 1024, // 5MB
        ],
        'thesis' => [
            'path' => '../../assets/documents/thesis/',
            'allowed_types' => ['pdf'],
            'max_size' => 50 * 1024 * 1024, // 50MB
        ],
        'publications' => [
            'path' => '../../assets/documents/publications/',
            'allowed_types' => ['pdf'],
            'max_size' => 20 * 1024 * 1024, // 20MB
        ]
    ];

    $config = $upload_config[$category] ?? $upload_config['images'];

    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
        exit;
    }

    $file = $_FILES['file'];

    // Basic file validation
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $config['allowed_types'])) {
        echo json_encode(['success' => false, 'message' => 'File type not allowed. Allowed: ' . implode(', ', $config['allowed_types'])]);
        exit;
    }

    if ($file['size'] > $config['max_size']) {
        echo json_encode(['success' => false, 'message' => 'File too large. Max size: ' . number_format($config['max_size'] / 1024 / 1024, 1) . 'MB']);
        exit;
    }

    // Create directory if it doesn't exist
    if (!is_dir($config['path'])) {
        mkdir($config['path'], 0755, true);
    }

    // Generate simple, clean filename with timestamp
    $timestamp = date('Ymd_His');
    $random = substr(md5(uniqid()), 0, 6);
    $filename = $timestamp . '_' . $random . '.' . $extension;
    $filepath = $config['path'] . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Convert to web-accessible path (remove ../../ and convert to assets/...)
        $relative_path = str_replace(['../../', '\\'], ['', '/'], $filepath);

        // Generate simple preview
        $is_image = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        $preview = '<div class="file-preview-container p-2 border rounded">';
        $preview .= '<div class="d-flex align-items-center gap-2">';

        if ($is_image) {
            $preview .= '<img src="' . htmlspecialchars($relative_path) . '" alt="Preview" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">';
        } else {
            $preview .= '<div style="width: 50px; height: 50px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">';
            $preview .= '<i class="fas fa-file-' . ($extension === 'pdf' ? 'pdf' : 'alt') . '" style="color: ' . ($extension === 'pdf' ? '#dc3545' : '#6c757d') . ';"></i>';
            $preview .= '</div>';
        }

        $preview .= '<div class="flex-grow-1">';
        $preview .= '<small class="text-muted d-block">' . htmlspecialchars(basename($relative_path)) . '</small>';
        $preview .= '<small class="text-primary">' . htmlspecialchars($relative_path) . '</small>';
        $preview .= '</div></div></div>';

        echo json_encode([
            'success' => true,
            'message' => 'File uploaded successfully',
            'file_path' => $relative_path,
            'filename' => $filename,
            'preview' => $preview
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
exit;
?>
