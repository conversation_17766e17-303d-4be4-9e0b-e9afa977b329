<?php
// Include the file upload widget
require_once 'components/FileUploadWidget.php';
$uploadWidget = new FileUploadWidget();
?>

<!-- Personal Information Section -->
<div id="personal" class="content-section active">
    <h3>Personal Information</h3>
    <form method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
        <input type="hidden" name="action" value="update_personal">

        <!-- Profile Image Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-circle"></i> Profile Image</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profile_image" class="form-label">Profile Image (Circular)</label>
                                    <?php echo $uploadWidget->render('images', 'profile_image', $data['personal']['profile_image'] ?? '', 'profile_image_widget'); ?>
                                    <small class="text-muted">This circular image appears in navigation and profile sections.</small>
                                </div>
                                <div class="text-center mt-2">
                                    <div class="current-profile-preview">
                                        <h6>Current Profile Image:</h6>
                                        <?php if (!empty($data['personal']['profile_image'])): ?>
                                            <img src="../<?php echo htmlspecialchars($data['personal']['profile_image']); ?>"
                                                 alt="Current Profile"
                                                 class="img-thumbnail"
                                                 style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%;"
                                                 onclick="showImageModal('../<?php echo htmlspecialchars($data['personal']['profile_image']); ?>', 'Current Profile Image')">
                                        <?php else: ?>
                                            <div class="placeholder-image" style="width: 80px; height: 80px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                                <i class="fas fa-user fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="about_image" class="form-label">About Me Image (Rectangular)</label>
                                    <?php echo $uploadWidget->render('images', 'about_image', $data['personal']['about_image'] ?? '', 'about_image_widget'); ?>
                                    <small class="text-muted">This larger image appears in the "About Me" section background.</small>
                                </div>
                                <div class="text-center mt-2">
                                    <div class="current-about-preview">
                                        <h6>Current About Me Image:</h6>
                                        <?php if (!empty($data['personal']['about_image'])): ?>
                                            <img src="../<?php echo htmlspecialchars($data['personal']['about_image']); ?>"
                                                 alt="Current About Image"
                                                 class="img-thumbnail"
                                                 style="width: 120px; height: 80px; object-fit: cover; border-radius: 8px;"
                                                 onclick="showImageModal('../<?php echo htmlspecialchars($data['personal']['about_image']); ?>', 'Current About Me Image')">
                                        <?php else: ?>
                                            <div class="placeholder-image" style="width: 120px; height: 80px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                                <i class="fas fa-image fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($data['personal']['name']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="title" class="form-label">Title</label>
                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($data['personal']['title']); ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="designation" class="form-label">Designation</label>
                    <input type="text" class="form-control" id="designation" name="designation" value="<?php echo htmlspecialchars($data['personal']['designation']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="experience_years" class="form-label">Experience Years</label>
                    <input type="number" class="form-control" id="experience_years" name="experience_years" value="<?php echo $data['personal']['experience_years']; ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="specialization" class="form-label">Specialization</label>
                    <input type="text" class="form-control" id="specialization" name="specialization" value="<?php echo htmlspecialchars($data['personal']['specialization']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="location" class="form-label">Location</label>
                    <input type="text" class="form-control" id="location" name="location" value="<?php echo htmlspecialchars($data['personal']['location']); ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($data['personal']['email']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="phone" class="form-label">Phone</label>
                    <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($data['personal']['phone']); ?>">
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="bio_short" class="form-label">Short Bio</label>
            <textarea class="form-control" id="bio_short" name="bio_short" rows="2"><?php echo htmlspecialchars($data['personal']['bio']['short']); ?></textarea>
        </div>
        <div class="mb-3">
            <label for="bio_detailed" class="form-label">Detailed Bio</label>
            <textarea class="form-control" id="bio_detailed" name="bio_detailed" rows="5"><?php echo htmlspecialchars($data['personal']['bio']['detailed']); ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Update Personal Info</button>
    </form>
</div>

<!-- Address Section -->
<div id="address" class="content-section">
    <h3>Address Information</h3>
    <form method="POST">
        <input type="hidden" name="action" value="update_address">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="line1" class="form-label">Address Line 1</label>
                    <input type="text" class="form-control" id="line1" name="line1" value="<?php echo htmlspecialchars($data['personal']['address']['line1']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="line2" class="form-label">Address Line 2</label>
                    <input type="text" class="form-control" id="line2" name="line2" value="<?php echo htmlspecialchars($data['personal']['address']['line2']); ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" id="city" name="city" value="<?php echo htmlspecialchars($data['personal']['address']['city']); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="state" class="form-label">State</label>
                    <input type="text" class="form-control" id="state" name="state" value="<?php echo htmlspecialchars($data['personal']['address']['state']); ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="country" class="form-label">Country</label>
                    <input type="text" class="form-control" id="country" name="country" value="<?php echo htmlspecialchars($data['personal']['address']['country']); ?>">
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="pincode" class="form-label">Pincode</label>
            <input type="text" class="form-control" id="pincode" name="pincode" value="<?php echo htmlspecialchars($data['personal']['address']['pincode']); ?>">
        </div>
        <button type="submit" class="btn btn-primary">Update Address</button>
    </form>
</div>

<!-- Contact Section -->
<div id="contact" class="content-section">
    <h3>Contact Information</h3>
    <form method="POST">
        <input type="hidden" name="action" value="update_contact">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="contact_email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="contact_email" name="email" value="<?php echo htmlspecialchars($data['contact']['email']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="contact_phone" class="form-label">Phone</label>
                    <input type="text" class="form-control" id="contact_phone" name="phone" value="<?php echo htmlspecialchars($data['contact']['phone']); ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="office_hours" class="form-label">Office Hours</label>
                    <input type="text" class="form-control" id="office_hours" name="office_hours" value="<?php echo htmlspecialchars($data['contact']['office_hours']); ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="response_time" class="form-label">Response Time</label>
                    <input type="text" class="form-control" id="response_time" name="response_time" value="<?php echo htmlspecialchars($data['contact']['response_time']); ?>">
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="preferred_contact" class="form-label">Preferred Contact Method</label>
            <select class="form-control" id="preferred_contact" name="preferred_contact">
                <option value="email" <?php echo $data['contact']['preferred_contact'] === 'email' ? 'selected' : ''; ?>>Email</option>
                <option value="phone" <?php echo $data['contact']['preferred_contact'] === 'phone' ? 'selected' : ''; ?>>Phone</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">Update Contact Info</button>
    </form>
</div>

<!-- Statistics Section -->
<div id="statistics" class="content-section">
    <h3>Statistics</h3>
    <form method="POST">
        <input type="hidden" name="action" value="update_statistics">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="stats_experience_years" class="form-label">Experience Years</label>
                    <input type="number" class="form-control" id="stats_experience_years" name="experience_years" value="<?php echo $data['statistics']['experience_years']; ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="projects_completed" class="form-label">Projects Completed</label>
                    <input type="number" class="form-control" id="projects_completed" name="projects_completed" value="<?php echo $data['statistics']['projects_completed']; ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="publications" class="form-label">Publications</label>
                    <input type="number" class="form-control" id="publications" name="publications" value="<?php echo $data['statistics']['publications']; ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="patents" class="form-label">Patents</label>
                    <input type="number" class="form-control" id="patents" name="patents" value="<?php echo $data['statistics']['patents']; ?>">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="awards" class="form-label">Awards</label>
                    <input type="number" class="form-control" id="awards" name="awards" value="<?php echo $data['statistics']['awards']; ?>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="students_mentored" class="form-label">Students Mentored</label>
                    <input type="number" class="form-control" id="students_mentored" name="students_mentored" value="<?php echo $data['statistics']['students_mentored']; ?>">
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Update Statistics</button>
    </form>
</div>

<!-- Social Links Section -->
<div id="social" class="content-section">
    <h3>Social Links</h3>
    <form method="POST">
        <input type="hidden" name="action" value="update_social">
        <div class="mb-3">
            <label for="linkedin" class="form-label">LinkedIn</label>
            <input type="url" class="form-control" id="linkedin" name="linkedin" value="<?php echo htmlspecialchars($data['social_links']['linkedin']); ?>">
        </div>
        <div class="mb-3">
            <label for="twitter" class="form-label">Twitter</label>
            <input type="url" class="form-control" id="twitter" name="twitter" value="<?php echo htmlspecialchars($data['social_links']['twitter']); ?>">
        </div>
        <div class="mb-3">
            <label for="researchgate" class="form-label">ResearchGate</label>
            <input type="url" class="form-control" id="researchgate" name="researchgate" value="<?php echo htmlspecialchars($data['social_links']['researchgate']); ?>">
        </div>
        <div class="mb-3">
            <label for="orcid" class="form-label">ORCID</label>
            <input type="url" class="form-control" id="orcid" name="orcid" value="<?php echo htmlspecialchars($data['social_links']['orcid']); ?>">
        </div>
        <div class="mb-3">
            <label for="google_scholar" class="form-label">Google Scholar</label>
            <input type="url" class="form-control" id="google_scholar" name="google_scholar" value="<?php echo htmlspecialchars($data['social_links']['google_scholar']); ?>">
        </div>
        <button type="submit" class="btn btn-primary">Update Social Links</button>
    </form>
</div>

<!-- Array-based sections (redirect to array manager) -->
<div id="education" class="content-section">
    <h3>Education Management</h3>
    <p>Manage education records with add, edit, and delete functionality.</p>
    <a href="array_manager.php?section=education" class="btn btn-primary">
        <i class="fas fa-graduation-cap"></i> Manage Education Records
    </a>
</div>

<div id="publications" class="content-section">
    <h3>Publications Management</h3>
    <p>Manage publications with add, edit, and delete functionality.</p>
    <a href="array_manager.php?section=publications" class="btn btn-primary">
        <i class="fas fa-book"></i> Manage Publications
    </a>
</div>

<div id="advisors" class="content-section">
    <h3>Advisors Management</h3>
    <p>Manage advisors with add, edit, and delete functionality.</p>
    <a href="array_manager.php?section=advisors" class="btn btn-primary">
        <i class="fas fa-users"></i> Manage Advisors
    </a>
</div>

<div id="gallery" class="content-section">
    <h3>Gallery Management</h3>
    <p>Manage gallery items with add, edit, and delete functionality.</p>
    <a href="array_manager.php?section=gallery" class="btn btn-primary">
        <i class="fas fa-images"></i> Manage Gallery
    </a>
</div>

<div id="videos" class="content-section">
    <h3>YouTube Videos Management</h3>
    <p>Manage YouTube videos with add, edit, and delete functionality.</p>
    <a href="array_manager.php?section=videos" class="btn btn-primary">
        <i class="fas fa-video"></i> Manage Videos
    </a>
</div>

<div id="journey" class="content-section">
    <h3>Journey Management</h3>
    <p>Manage journey timeline with add, edit, and delete functionality.</p>
    <a href="array_manager.php?section=journey" class="btn btn-primary">
        <i class="fas fa-road"></i> Manage Journey
    </a>
</div>

<div id="thesis" class="content-section">
    <h3>Thesis Management</h3>
    <p>Manage multiple thesis records with add, edit, and delete functionality.</p>
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Current Thesis Records</h5>
                    <p class="card-text">Total: <?php
                        $thesisCount = 0;
                        if (isset($data['thesis'])) {
                            if (is_array($data['thesis']) && isset($data['thesis'][0])) {
                                $thesisCount = count($data['thesis']);
                            } elseif (isset($data['thesis']['title'])) {
                                $thesisCount = 1;
                            }
                        }
                        echo $thesisCount;
                    ?> thesis records</p>
                    <a href="thesis_manager.php" class="btn btn-primary">
                        <i class="fas fa-graduation-cap"></i> Manage Thesis
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Quick Actions</h5>
                    <p class="card-text">Add, edit, or delete thesis records</p>
                    <a href="thesis_manager.php" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New Thesis
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
