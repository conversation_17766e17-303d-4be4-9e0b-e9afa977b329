<?php
// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $gallery = $dataLoader->getGallery();
    $seo = $dataLoader->getMetaTags();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta Debbarma'];
    $gallery = [];
    $seo = ['title' => 'Gallery - Dr. Jayanta Debbarma'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?> - Gallery</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1a3a5f;
            --secondary-color: #4b7bec;
            --accent-color: #f39c12;
            --text-color: #333;
            --light-gray: #f8f9fa;
            --dark-gray: #343a40;
            --white: #ffffff;
            --section-padding: 80px 0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            overflow-x: hidden;
            background-color: var(--light-gray);
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .section {
            padding: var(--section-padding);
        }
        
        /* Header */
        header {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo i {
            font-size: 30px;
            color: var(--accent-color);
        }
        
        .logo h1 {
            font-size: 24px;
            color: var(--white);
            margin-bottom: 0;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background-color: var(--secondary-color);
            color: var(--white);
            text-decoration: none;
            border-radius: 30px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        
        .back-btn:hover {
            background-color: var(--accent-color);
        }
        
        /* Gallery Section */
        .gallery {
            background-color: var(--white);
        }
        
        .gallery-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .gallery-tabs {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 30px;
        }
        
        .gallery-tab {
            padding: 10px 20px;
            margin: 0 5px;
            background-color: var(--light-gray);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .gallery-tab.active {
            background-color: var(--secondary-color);
            color: var(--white);
        }
        
        .gallery-tab-content {
            display: none;
        }
        
        .gallery-tab-content.active {
            display: block;
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .gallery-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            height: 250px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: translateY(-10px);
        }
        
        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover img {
            transform: scale(1.05);
        }
        
        .gallery-item-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            padding: 20px;
            text-align: center;
        }
        
        .gallery-item:hover .gallery-item-overlay {
            opacity: 1;
        }
        
        .gallery-item-title {
            color: var(--white);
            font-size: 1.4rem;
            margin-bottom: 15px;
        }
        
        .gallery-item-description {
            color: var(--white);
            font-size: 1rem;
            margin-bottom: 20px;
        }
        
        .gallery-item-link {
            display: inline-block;
            padding: 8px 16px;
            background-color: var(--secondary-color);
            color: var(--white);
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        
        .gallery-item-link:hover {
            background-color: var(--accent-color);
        }
        
        /* Detail Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: var(--white);
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            max-width: 800px;
            position: relative;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 28px;
            color: var(--dark-gray);
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .close-modal:hover {
            color: var(--primary-color);
        }
        
        .modal-header {
            margin-bottom: 20px;
        }
        
        .modal-body {
            margin-bottom: 30px;
        }
        
        .modal-image {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: var(--secondary-color);
            color: var(--white);
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            cursor: pointer;
            border: none;
        }
        
        .modal-btn:hover {
            background-color: var(--primary-color);
        }
        
        /* Footer */
        footer {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 50px 0 20px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .footer-column h3 {
            color: var(--white);
            margin-bottom: 20px;
            position: relative;
        }
        
        .footer-column h3::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -10px;
            width: 50px;
            height: 3px;
            background-color: var(--accent-color);
        }
        
        .footer-links {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: var(--white);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: var(--accent-color);
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-links a {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: var(--white);
            transition: background-color 0.3s ease;
        }
        
        .social-links a:hover {
            background-color: var(--accent-color);
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .gallery-tabs {
                flex-wrap: wrap;
            }
            
            .gallery-tab {
                margin-bottom: 10px;
            }
            
            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
            
            .modal-content {
                margin: 20px;
                padding: 20px;
            }
        }
        
        @media (max-width: 576px) {
            .section {
                padding: 60px 0;
            }
            
            .gallery-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-user-graduate"></i>
                    <h1><?php echo escape($personal['name'] ?? 'Dr. Jayanta Debbarma'); ?></h1>
                </div>
                <a href="index.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i> Back to Main Page
                </a>
            </div>
            <div style="text-align: center; margin-top: 30px;">
                <a href="about-more.php" style="color: var(--primary-color); text-decoration: none; margin: 0 15px;">About</a>
                <a href="journey-more.php" style="color: var(--primary-color); text-decoration: none; margin: 0 15px;">Journey</a>
                <a href="publications-more.php" style="color: var(--primary-color); text-decoration: none; margin: 0 15px;">Publications</a>
                <a href="thesis-more.php" style="color: var(--primary-color); text-decoration: none; margin: 0 15px;">Thesis</a>
                <a href="advisors-more.php" style="color: var(--primary-color); text-decoration: none; margin: 0 15px;">Advisors</a>
            </div>
        </div>
    </header>

    <!-- Gallery Section -->
    <section class="gallery section" data-aos="fade-up">
        <div class="container">
            <div class="gallery-header">
                <h2>Gallery</h2>
                <p>Explore the complete collection of documents, images, and videos.</p>
            </div>
            
            <div class="gallery-tabs">
                <button class="gallery-tab active" data-tab="images">Images</button>
                <button class="gallery-tab" data-tab="all">All Items</button>
                <button class="gallery-tab" data-tab="documents">Documents</button>
                <button class="gallery-tab" data-tab="research">Research</button>
                <button class="gallery-tab" data-tab="projects">Projects</button>
                <button class="gallery-tab" data-tab="videos">Videos</button>
            </div>

            <div class="gallery-tab-content active" id="images">
                <div class="gallery-grid">
                    <?php
                    // Filter for image type items
                    $imageItems = array_filter($gallery, function($item) {
                        return isset($item['type']) && $item['type'] === 'image';
                    });
                    echo renderGalleryMore($imageItems);
                    ?>
                </div>
            </div>

            <div class="gallery-tab-content" id="all">
                <div class="gallery-grid">
                    <?php echo renderGalleryMore($gallery); ?>
                </div>
            </div>

            <div class="gallery-tab-content" id="documents">
                <div class="gallery-grid">
                    <?php echo renderGalleryMore($gallery, 'documents'); ?>
                </div>
            </div>

            <div class="gallery-tab-content" id="research">
                <div class="gallery-grid">
                    <?php echo renderGalleryMore($gallery, 'research'); ?>
                </div>
            </div>

            <div class="gallery-tab-content" id="projects">
                <div class="gallery-grid">
                    <?php echo renderGalleryMore($gallery, 'projects'); ?>
                </div>
            </div>

            <div class="gallery-tab-content" id="videos">
                <div class="gallery-grid">
                    <?php echo renderGalleryMore($gallery, 'videos'); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Detail Modal -->
    <div class="modal" id="detail-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="modal-header">
                <h2 id="modal-title">Item Title</h2>
            </div>
            <div class="modal-body">
                <img id="modal-image" src="" alt="" class="modal-image">
                <p id="modal-description">Item description will appear here.</p>
            </div>
            <div class="modal-footer">
                <a id="modal-link" href="#" class="modal-btn" target="_blank">View Full Document</a>
                <button id="modal-close" class="modal-btn">Close</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer data-aos="fade-up">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>About Me</h3>
                    <p>Senior engineer with 30+ years of experience in innovation and research. Passionate about developing sustainable engineering solutions for a better future.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-google-scholar"></i></a>
                        <a href="#"><i class="fab fa-researchgate"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="index.php#journey">Journey</a></li>
                        <li><a href="index.php#publications">Publications</a></li>
                        <li><a href="index.php#thesis">Thesis & Chapters</a></li>
                        <li><a href="index.php#advisors">Key Advisors</a></li>
                        <li><a href="index.php#gallery">Field of Interest</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Information</h3>
                    <ul class="footer-links">
                        <li><i class="fas fa-map-marker-alt"></i> 123 Engineering Lane, Tech City</li>
                        <li><i class="fas fa-phone"></i> +****************</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Dr. John Smith. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Gallery Tabs
        const galleryTabs = document.querySelectorAll('.gallery-tab');
        const galleryTabContents = document.querySelectorAll('.gallery-tab-content');

        galleryTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // Remove active class from all tabs and contents
                galleryTabs.forEach(t => t.classList.remove('active'));
                galleryTabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Modal functionality
        const modal = document.getElementById('detail-modal');
        const closeModal = document.querySelector('.close-modal');
        const modalCloseBtn = document.getElementById('modal-close');
        
        // Open modal when view details is clicked
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('view-details')) {
                e.preventDefault();
                const galleryItem = e.target.closest('.gallery-item');
                const title = galleryItem.querySelector('.gallery-item-title').textContent;
                const description = galleryItem.querySelector('.gallery-item-description').textContent;
                const imageSrc = galleryItem.querySelector('img').src;
                
                // Populate modal with item data
                document.getElementById('modal-title').textContent = title;
                document.getElementById('modal-image').src = imageSrc;
                document.getElementById('modal-image').alt = title;
                document.getElementById('modal-description').textContent = description;
                document.getElementById('modal-link').href = '#'; // In a real implementation, this would link to the actual resource
                
                // Show modal
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        });
        
        // Close modal when close button is clicked
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
        
        modalCloseBtn.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
        
        // Close modal when clicking outside of it
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    </script>
</body>
</html>