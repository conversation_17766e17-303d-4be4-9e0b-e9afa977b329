<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

$action = $_POST['action'] ?? '';
$success_message = '';
$error_message = '';

// Convert single thesis object to array if needed
if (!isset($data['thesis'])) {
    $data['thesis'] = [];
} elseif (isset($data['thesis']['title'])) {
    // Single thesis object detected - convert to array
    $singleThesis = $data['thesis'];
    // Add ID if missing
    if (!isset($singleThesis['id'])) {
        $singleThesis['id'] = 1;
    }
    $data['thesis'] = [$singleThesis];
} elseif (is_array($data['thesis']) && !empty($data['thesis'])) {
    // Array of thesis - ensure all have IDs
    foreach ($data['thesis'] as $index => &$thesis) {
        if (!isset($thesis['id'])) {
            $thesis['id'] = $index + 1;
        }
    }
    unset($thesis); // Break reference
} else {
    // Empty or invalid thesis data
    $data['thesis'] = [];
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'add_thesis':
            $max_id = 0;
            foreach ($data['thesis'] as $thesis) {
                if (isset($thesis['id']) && is_numeric($thesis['id']) && (int)$thesis['id'] > $max_id) {
                    $max_id = (int)$thesis['id'];
                }
            }
            // If no thesis exist yet, start with ID 1
            if (empty($data['thesis'])) {
                $max_id = 0;
            }
            $new_item = [
                'id' => $max_id + 1,
                'title' => $_POST['title'],
                'year' => (int)$_POST['year'],
                'university' => $_POST['university'],
                'supervisor' => $_POST['supervisor'],
                'pages' => (int)$_POST['pages'],
                'abstract' => $_POST['abstract'],
                'pdf_file' => $_POST['pdf_file'],
                'cover_image' => $_POST['cover_image'],
                'keywords' => array_map('trim', explode(',', $_POST['keywords'])),
                'degree' => $_POST['degree'] ?? 'Ph.D.',
                'department' => $_POST['department'] ?? ''
            ];
            $data['thesis'][] = $new_item;
            $success_message = "Thesis added successfully!";
            break;
            
        case 'edit_thesis':
            $index = (int)$_POST['index'];
            if (isset($data['thesis'][$index])) {
                $data['thesis'][$index] = [
                    'id' => $data['thesis'][$index]['id'] ?? ($index + 1),
                    'title' => $_POST['title'],
                    'year' => (int)$_POST['year'],
                    'university' => $_POST['university'],
                    'supervisor' => $_POST['supervisor'],
                    'pages' => (int)$_POST['pages'],
                    'abstract' => $_POST['abstract'],
                    'pdf_file' => $_POST['pdf_file'],
                    'cover_image' => $_POST['cover_image'],
                    'keywords' => array_map('trim', explode(',', $_POST['keywords'])),
                    'degree' => $_POST['degree'] ?? 'Ph.D.',
                    'department' => $_POST['department'] ?? ''
                ];
                $success_message = "Thesis updated successfully!";
            } else {
                $error_message = "Thesis not found!";
            }
            break;
            
        case 'delete_thesis':
            $index = (int)$_POST['index'];
            if (isset($data['thesis'][$index])) {
                array_splice($data['thesis'], $index, 1);
                $success_message = "Thesis deleted successfully!";
            } else {
                $error_message = "Thesis not found!";
            }
            break;
    }
    
    // Save updated data
    if ($success_message) {
        // Create backup
        $backup_file = '../data/backups/site-data-' . date('Y-m-d-H-i-s') . '.json';
        if (!is_dir('../data/backups')) {
            mkdir('../data/backups', 0755, true);
        }
        copy($json_file, $backup_file);
        
        // Save new data
        file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thesis Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-light bg-light">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="dashboard_new.php">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <h4 class="mb-0">Thesis Management</h4>
                    </div>
                </nav>
                
                <div class="container mt-4">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <h3>Thesis Management</h3>
                    <p class="text-muted">Total Thesis Records: <?php echo count($data['thesis']); ?></p>

                    <!-- Add New Thesis Form -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-plus"></i> Add New Thesis</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="add_thesis">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title *</label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="year" class="form-label">Year *</label>
                                            <input type="number" class="form-control" id="year" name="year" min="1900" max="2030" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="university" class="form-label">University *</label>
                                            <input type="text" class="form-control" id="university" name="university" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="supervisor" class="form-label">Supervisor *</label>
                                            <input type="text" class="form-control" id="supervisor" name="supervisor" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="degree" class="form-label">Degree</label>
                                            <select class="form-control" id="degree" name="degree">
                                                <option value="Ph.D.">Ph.D.</option>
                                                <option value="M.Tech">M.Tech</option>
                                                <option value="M.S.">M.S.</option>
                                                <option value="M.Phil">M.Phil</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="pages" class="form-label">Pages</label>
                                            <input type="number" class="form-control" id="pages" name="pages" min="1">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="department" class="form-label">Department</label>
                                            <input type="text" class="form-control" id="department" name="department">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="abstract" class="form-label">Abstract *</label>
                                    <textarea class="form-control" id="abstract" name="abstract" rows="4" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="pdf_file" class="form-label">PDF File Path</label>
                                            <input type="text" class="form-control" id="pdf_file" name="pdf_file" placeholder="assets/documents/thesis/thesis.pdf">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="cover_image" class="form-label">Cover Image Path</label>
                                            <input type="text" class="form-control" id="cover_image" name="cover_image" placeholder="assets/images/thesis/cover.jpg">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="keywords" class="form-label">Keywords (comma separated)</label>
                                    <input type="text" class="form-control" id="keywords" name="keywords" placeholder="Research, Engineering, Technology">
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add Thesis
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Existing Thesis Records -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Existing Thesis Records (<?php echo count($data['thesis']); ?>)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($data['thesis'])): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No thesis records found. Add your first thesis using the form above.
                                </div>
                            <?php else: ?>
                                <?php foreach ($data['thesis'] as $index => $thesis): ?>
                                    <div class="border p-3 mb-3 rounded">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="text-primary">Thesis #<?php echo $thesis['id'] ?? ($index + 1); ?></h6>
                                            <span class="badge bg-secondary"><?php echo $thesis['year']; ?></span>
                                        </div>
                                        
                                        <form method="POST" class="mb-3">
                                            <input type="hidden" name="action" value="edit_thesis">
                                            <input type="hidden" name="index" value="<?php echo $index; ?>">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="mb-3">
                                                        <label class="form-label">Title</label>
                                                        <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($thesis['title']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Year</label>
                                                        <input type="number" class="form-control" name="year" value="<?php echo $thesis['year']; ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">University</label>
                                                        <input type="text" class="form-control" name="university" value="<?php echo htmlspecialchars($thesis['university']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Supervisor</label>
                                                        <input type="text" class="form-control" name="supervisor" value="<?php echo htmlspecialchars($thesis['supervisor']); ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Degree</label>
                                                        <select class="form-control" name="degree">
                                                            <option value="Ph.D." <?php echo ($thesis['degree'] ?? 'Ph.D.') == 'Ph.D.' ? 'selected' : ''; ?>>Ph.D.</option>
                                                            <option value="M.Tech" <?php echo ($thesis['degree'] ?? '') == 'M.Tech' ? 'selected' : ''; ?>>M.Tech</option>
                                                            <option value="M.S." <?php echo ($thesis['degree'] ?? '') == 'M.S.' ? 'selected' : ''; ?>>M.S.</option>
                                                            <option value="M.Phil" <?php echo ($thesis['degree'] ?? '') == 'M.Phil' ? 'selected' : ''; ?>>M.Phil</option>
                                                            <option value="Other" <?php echo ($thesis['degree'] ?? '') == 'Other' ? 'selected' : ''; ?>>Other</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Pages</label>
                                                        <input type="number" class="form-control" name="pages" value="<?php echo $thesis['pages'] ?? ''; ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Department</label>
                                                        <input type="text" class="form-control" name="department" value="<?php echo htmlspecialchars($thesis['department'] ?? ''); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Abstract</label>
                                                <textarea class="form-control" name="abstract" rows="3" required><?php echo htmlspecialchars($thesis['abstract']); ?></textarea>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">PDF File Path</label>
                                                        <input type="text" class="form-control" name="pdf_file" value="<?php echo htmlspecialchars($thesis['pdf_file'] ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Cover Image Path</label>
                                                        <input type="text" class="form-control" name="cover_image" value="<?php echo htmlspecialchars($thesis['cover_image'] ?? ''); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Keywords (comma separated)</label>
                                                <input type="text" class="form-control" name="keywords" value="<?php echo htmlspecialchars(implode(', ', $thesis['keywords'] ?? [])); ?>">
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-save"></i> Update
                                                </button>
                                            </div>
                                        </form>
                                        
                                        <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this thesis?')">
                                            <input type="hidden" name="action" value="delete_thesis">
                                            <input type="hidden" name="index" value="<?php echo $index; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
