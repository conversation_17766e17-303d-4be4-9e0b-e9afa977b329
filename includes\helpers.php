<?php
/**
 * Helper Functions for Portfolio Website
 * Contains utility functions for rendering components and formatting data
 */

/**
 * Safely escape HTML output
 */
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Render social links
 */
function renderSocialLinks($socialLinks) {
    $html = '';
    $iconMap = [
        'linkedin' => 'fab fa-linkedin-in',
        'twitter' => 'fab fa-twitter',
        'researchgate' => 'fab fa-researchgate',
        'orcid' => 'fab fa-orcid',
        'google_scholar' => 'fas fa-graduation-cap',
        'email' => 'fas fa-envelope'
    ];
    
    foreach ($socialLinks as $platform => $url) {
        if (!empty($url)) {
            $icon = $iconMap[$platform] ?? 'fas fa-link';
            $html .= '<a href="' . escape($url) . '" class="social-link" target="_blank" rel="noopener">';
            $html .= '<i class="' . $icon . '"></i>';
            $html .= '</a>';
        }
    }
    
    return $html;
}

/**
 * Render statistics
 */
function renderStatistics($stats) {
    $html = '';
    $statItems = [
        ['value' => ($stats['experience_years'] ?? 30) . '+', 'label' => 'Years Experience'],
        ['value' => ($stats['projects_completed'] ?? 150) . '+', 'label' => 'Projects Completed'],
        ['value' => ($stats['publications'] ?? 50) . '+', 'label' => 'Publications']
    ];
    
    foreach ($statItems as $stat) {
        $html .= '<div class="stat-item">';
        $html .= '<span class="stat-number">' . escape($stat['value']) . '</span>';
        $html .= '<span class="stat-label">' . escape($stat['label']) . '</span>';
        $html .= '</div>';
    }
    
    return $html;
}

/**
 * Render publications with limit
 */
function renderPublications($publications, $limit = 6) {
    $html = '';
    $displayItems = array_slice($publications, 0, $limit);

    foreach ($displayItems as $pub) {
        $html .= '<div class="journal-card" data-aos="zoom-in">';
        $html .= '<div class="journal-card-body">';
        $html .= '<h3 class="journal-title">' . escape($pub['title']) . '</h3>';
        $html .= '<div class="journal-meta">';
        $html .= '<span>' . escape($pub['journal']) . '</span>';
        $html .= '<span>' . escape($pub['year']) . '</span>';
        $html .= '</div>';
        $html .= '<p>' . escape($pub['abstract']) . '</p>';

        if (!empty($pub['pdf_file'])) {
            $html .= '<a href="' . escape($pub['pdf_file']) . '" class="journal-link" target="_blank">';
            $html .= 'Read More <i class="fas fa-arrow-right"></i>';
            $html .= '</a>';
        }

        $html .= '</div>';
        $html .= '</div>';
    }

    return $html;
}

/**
 * Render advisors with limit
 */
function renderAdvisors($advisors, $limit = 4) {
    $html = '';
    $delay = 0;
    $displayItems = array_slice($advisors, 0, $limit);

    foreach ($displayItems as $advisor) {
        $html .= '<div class="advisor-card" data-aos="zoom-in" data-aos-delay="' . $delay . '">';
        $html .= '<div class="advisor-image">';

        if (!empty($advisor['image']) && file_exists($advisor['image'])) {
            $html .= '<img src="' . escape($advisor['image']) . '" alt="' . escape($advisor['name']) . '">';
        } else {
            $html .= '<img src="https://picsum.photos/seed/' . urlencode($advisor['name']) . '/300/300.jpg" alt="' . escape($advisor['name']) . '">';
        }

        $html .= '</div>';
        $html .= '<h3 class="advisor-name">' . escape($advisor['name']) . '</h3>';
        $html .= '<p class="advisor-title">' . escape($advisor['title']) . '</p>';
        $html .= '<p class="advisor-bio">' . escape($advisor['bio']) . '</p>';
        $html .= '</div>';

        $delay += 100;
    }

    return $html;
}

/**
 * Render journey timeline
 */
function renderJourney($journey) {
    $html = '';
    $isRight = false;
    
    foreach ($journey as $item) {
        $animationClass = $isRight ? 'fade-left' : 'fade-right';
        
        $html .= '<div class="timeline-item" data-aos="' . $animationClass . '">';
        $html .= '<div class="timeline-dot"></div>';
        $html .= '<div class="timeline-content">';
        $html .= '<div class="timeline-year">' . escape($item['year']) . '</div>';
        $html .= '<h3>' . escape($item['title']) . '</h3>';
        $html .= '<p>' . escape($item['description']) . '</p>';
        $html .= '</div>';
        $html .= '</div>';
        
        $isRight = !$isRight;
    }
    
    return $html;
}

/**
 * Render gallery items with limit
 */
function renderGallery($galleryItems, $category = null, $limit = 6) {
    $html = '';
    $delay = 0;

    $items = $category ? array_filter($galleryItems, function($item) use ($category) {
        return isset($item['category']) && $item['category'] === $category;
    }) : $galleryItems;

    $displayItems = array_slice($items, 0, $limit);

    foreach ($displayItems as $item) {
        $type = $item['type'] ?? 'image';
        $link = $item['link'] ?? '#';

        $html .= '<div class="gallery-item" data-aos="zoom-in" data-aos-delay="' . $delay . '" onclick="openGalleryItem(\'' . escape($link) . '\', \'' . escape($type) . '\')">';

        // Thumbnail image
        if (!empty($item['image'])) {
            $html .= '<img src="' . escape($item['image']) . '" alt="' . escape($item['title']) . '" onerror="this.src=\'https://picsum.photos/seed/' . urlencode($item['title']) . '/300/200.jpg\'">';
        } else {
            $html .= '<img src="https://picsum.photos/seed/' . urlencode($item['title']) . '/300/200.jpg" alt="' . escape($item['title']) . '">';
        }

        // Type icon overlay
        $html .= '<div class="gallery-type-icon">';
        switch ($type) {
            case 'video':
                $html .= '<i class="fas fa-play-circle"></i>';
                break;
            case 'pdf':
                $html .= '<i class="fas fa-file-pdf"></i>';
                break;
            default:
                $html .= '<i class="fas fa-image"></i>';
                break;
        }
        $html .= '</div>';

        $html .= '<div class="gallery-item-overlay">';
        $html .= '<h3 class="gallery-item-title">' . escape($item['title']) . '</h3>';
        if (!empty($item['description'])) {
            $html .= '<p class="gallery-item-description">' . escape(truncateText($item['description'], 80)) . '</p>';
        }
        $html .= '<div class="gallery-item-category">' . escape(ucfirst($item['category'] ?? 'General')) . '</div>';
        $html .= '</div>';
        $html .= '</div>';

        $delay += 100;
    }

    return $html;
}

/**
 * Render thesis information as book covers
 */
function renderThesis($thesisData) {
    if (empty($thesisData)) {
        return '';
    }

    $html = '';
    $delay = 0;

    // Handle both single thesis and array of thesis
    $theses = isset($thesisData['title']) ? [$thesisData] : $thesisData;

    foreach ($theses as $thesis) {
        $html .= '<div class="thesis-card" data-aos="fade-up" data-aos-delay="' . $delay . '">';

        // Card Header
        $html .= '<div class="thesis-card-header">';
        $html .= '<div class="university-badge">';
        $html .= '<i class="fas fa-graduation-cap"></i>';
        $html .= '<span>' . escape($thesis['university'] ?? 'University') . '</span>';
        $html .= '</div>';
        $html .= '<h3 class="thesis-title">' . escape($thesis['title'] ?? 'Research Thesis') . '</h3>';
        if (!empty($thesis['subtitle'])) {
            $html .= '<div class="thesis-subtitle">' . escape($thesis['subtitle']) . '</div>';
        }
        $html .= '</div>';

        // Card Body
        $html .= '<div class="thesis-card-body">';

        // Meta information
        $html .= '<div class="thesis-meta">';
        $html .= '<div class="thesis-author">By: ' . escape($thesis['author'] ?? 'Dr. Jayanta Debbarma') . '</div>';
        $html .= '<div>';
        $html .= '<span class="thesis-year">' . escape($thesis['year'] ?? date('Y')) . '</span>';
        if (!empty($thesis['degree'])) {
            $html .= '<span class="thesis-degree">' . escape($thesis['degree']) . '</span>';
        }
        $html .= '</div>';
        $html .= '</div>';

        // Abstract
        if (!empty($thesis['abstract'])) {
            $html .= '<div class="thesis-abstract">';
            $html .= escape(truncateText($thesis['abstract'], 150));
            $html .= '</div>';
        }

        // Supervisor info
        if (!empty($thesis['supervisor'])) {
            $html .= '<div class="thesis-supervisor">';
            $html .= '<strong>Supervisor:</strong> ' . escape($thesis['supervisor']);
            $html .= '</div>';
        }

        // Actions
        $html .= '<div class="thesis-actions">';
        if (!empty($thesis['pdf_file'])) {
            $html .= '<a href="' . escape($thesis['pdf_file']) . '" class="thesis-btn thesis-btn-primary" target="_blank">';
            $html .= '<i class="fas fa-download"></i> Download PDF';
            $html .= '</a>';
        }
        $html .= '<a href="thesis-more.php" class="thesis-btn thesis-btn-secondary">';
        $html .= '<i class="fas fa-eye"></i> View Details';
        $html .= '</a>';
        $html .= '</div>';

        $html .= '</div>'; // End card body
        $html .= '</div>'; // End thesis card

        $delay += 200;
    }

    return $html;
}

/**
 * Check if "View More" button should be shown
 */
function shouldShowViewMore($items, $limit) {
    return count($items) > $limit;
}

/**
 * Render gallery items for gallery-more.php page
 */
function renderGalleryMore($galleryItems, $category = null) {
    $html = '';
    $delay = 0;

    $items = $category ? array_filter($galleryItems, function($item) use ($category) {
        return isset($item['category']) && $item['category'] === $category;
    }) : $galleryItems;

    foreach ($items as $item) {
        $type = $item['type'] ?? 'image';
        $link = $item['link'] ?? '#';

        $html .= '<div class="gallery-item" data-aos="zoom-in" data-aos-delay="' . $delay . '">';

        // Thumbnail image
        if (!empty($item['image'])) {
            $html .= '<img src="' . escape($item['image']) . '" alt="' . escape($item['title']) . '" onerror="this.src=\'https://picsum.photos/seed/' . urlencode($item['title']) . '/300/200.jpg\'">';
        } else {
            $html .= '<img src="https://picsum.photos/seed/' . urlencode($item['title']) . '/300/200.jpg" alt="' . escape($item['title']) . '">';
        }

        $html .= '<div class="gallery-item-overlay">';
        $html .= '<h3 class="gallery-item-title">' . escape($item['title']) . '</h3>';
        if (!empty($item['description'])) {
            $html .= '<p class="gallery-item-description">' . escape($item['description']) . '</p>';
        }
        $html .= '<a href="' . escape($link) . '" class="gallery-item-link view-details" target="_blank">View Details</a>';
        $html .= '</div>';
        $html .= '</div>';

        $delay += 100;
    }

    return $html;
}

/**
 * Render YouTube videos slider with limit
 */
function renderYouTubeVideos($videosData, $limit = 6) {
    if (empty($videosData)) {
        return '<div class="video-card"><div class="video-content"><p>No videos available.</p></div></div>';
    }

    $html = '';
    $displayItems = array_slice($videosData, 0, $limit);

    foreach ($displayItems as $video) {
        $html .= '<div class="video-card" onclick="openYouTubeVideo(\'' . escape($video['youtube_id'] ?? '') . '\')">';

        // Video thumbnail
        $html .= '<div class="video-thumbnail">';
        if (!empty($video['thumbnail'])) {
            $html .= '<img src="' . escape($video['thumbnail']) . '" alt="' . escape($video['title'] ?? 'Video') . '" onerror="this.src=\'https://img.youtube.com/vi/' . escape($video['youtube_id'] ?? '') . '/maxresdefault.jpg\'">';
        } else {
            // Use YouTube thumbnail if no custom thumbnail
            $youtubeId = $video['youtube_id'] ?? '';
            $html .= '<img src="https://img.youtube.com/vi/' . escape($youtubeId) . '/maxresdefault.jpg" alt="' . escape($video['title'] ?? 'Video') . '">';
        }

        // Play button
        $html .= '<div class="play-button">';
        $html .= '<i class="fas fa-play"></i>';
        $html .= '</div>';

        // Duration
        if (!empty($video['duration'])) {
            $html .= '<div class="video-duration">' . escape($video['duration']) . '</div>';
        }
        $html .= '</div>';

        // Video content
        $html .= '<div class="video-content">';
        $html .= '<h3 class="video-title">' . escape($video['title'] ?? 'Untitled Video') . '</h3>';

        if (!empty($video['description'])) {
            $html .= '<p class="video-description">' . escape(truncateText($video['description'], 100)) . '</p>';
        }

        if (!empty($video['category'])) {
            $html .= '<span class="video-category">' . escape($video['category']) . '</span>';
        }

        $html .= '</div>';
        $html .= '</div>';
    }

    return $html;
}

/**
 * Render multiple thesis books in a bookshelf layout
 */
function renderThesisBookshelf($thesisData) {
    if (empty($thesisData)) {
        // Create placeholder books if no data
        $placeholderBooks = [
            [
                'title' => 'Engineering Innovations',
                'subtitle' => 'A Comprehensive Study',
                'author' => 'Dr. Jayanta Debbarma',
                'year' => '1995',
                'degree' => 'Ph.D.',
                'university' => 'MIT',
                'abstract' => 'A comprehensive exploration of groundbreaking engineering innovations that have shaped modern technology and their potential future applications.'
            ],
            [
                'title' => 'Sustainable Engineering',
                'subtitle' => 'Future Technologies',
                'author' => 'Dr. Jayanta Debbarma',
                'year' => '2005',
                'degree' => 'Post-Doc',
                'university' => 'Stanford University',
                'abstract' => 'Examining sustainable approaches to engineering challenges and their implementation in real-world scenarios for a greener future.'
            ]
        ];
        return renderThesis($placeholderBooks);
    }

    // Check if thesisData is a single object (not an array)
    if (isset($thesisData['title'])) {
        // Convert single thesis to array format and add a second placeholder book
        $thesisArray = [
            [
                'title' => $thesisData['title'],
                'subtitle' => $thesisData['subtitle'] ?? '',
                'author' => $thesisData['author'] ?? 'Dr. Jayanta Debbarma',
                'year' => $thesisData['year'] ?? date('Y'),
                'degree' => $thesisData['degree'] ?? 'Ph.D.',
                'university' => $thesisData['university'] ?? 'University',
                'abstract' => $thesisData['abstract'] ?? 'Research abstract not available.',
                'supervisor' => $thesisData['supervisor'] ?? '',
                'pdf_file' => $thesisData['pdf_file'] ?? ''
            ],
            [
                'title' => 'Sustainable Engineering',
                'subtitle' => 'Future Technologies',
                'author' => 'Dr. Jayanta Debbarma',
                'year' => '2005',
                'degree' => 'Post-Doc',
                'university' => 'Stanford University',
                'abstract' => 'Examining sustainable approaches to engineering challenges and their implementation in real-world scenarios for a greener future.'
            ]
        ];
        return renderThesis($thesisArray);
    }

    return renderThesis($thesisData);
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Check if image exists and return appropriate src
 */
function getImageSrc($imagePath, $fallback = null) {
    if (!empty($imagePath) && file_exists($imagePath)) {
        return $imagePath;
    }
    return $fallback;
}

/**
 * Generate breadcrumb navigation
 */
function generateBreadcrumb($currentPage = 'Home') {
    $html = '<nav class="breadcrumb">';
    $html .= '<a href="index.php">Home</a>';
    
    if ($currentPage !== 'Home') {
        $html .= ' / <span>' . escape($currentPage) . '</span>';
    }
    
    $html .= '</nav>';
    return $html;
}

/**
 * Truncate text with ellipsis
 */
function truncateText($text, $length = 150) {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . '...';
}
?>
