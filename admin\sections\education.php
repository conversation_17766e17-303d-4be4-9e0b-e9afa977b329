<?php
// Include the file upload widget
require_once __DIR__ . '/../components/FileUploadWidget.php';
$uploadWidget = new FileUploadWidget();
?>

<h3>Education Management</h3>

<!-- Add New Education Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5>Add New Education</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="add_education">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="institution" class="form-label">Institution</label>
                        <input type="text" class="form-control" id="institution" name="institution" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="year" class="form-label">Year</label>
                        <input type="text" class="form-control" id="year" name="year" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="certificate" class="form-label">Certificate/Document</label>
                        <?php echo $uploadWidget->render('documents', 'certificate', '', 'education_certificate'); ?>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success">Add Education</button>
        </form>
    </div>
</div>

<!-- Existing Education Items -->
<div class="card">
    <div class="card-header">
        <h5>Existing Education Records</h5>
    </div>
    <div class="card-body">
        <?php foreach ($data['education'] as $index => $education): ?>
            <div class="border p-3 mb-3">
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="edit_education">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="title" value="<?php echo htmlspecialchars($education['title']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Institution</label>
                                <input type="text" class="form-control" name="institution" value="<?php echo htmlspecialchars($education['institution']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Year</label>
                                <input type="text" class="form-control" name="year" value="<?php echo htmlspecialchars($education['year']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm">Update</button>
                    </div>
                </form>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="delete_education">
                    <input type="hidden" name="index" value="<?php echo $index; ?>">
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this education record?')">Delete</button>
                </form>
            </div>
        <?php endforeach; ?>
    </div>
</div>
