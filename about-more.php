<?php
// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $education = $dataLoader->getEducation();
    $statistics = $dataLoader->getStatistics();
    $seo = $dataLoader->getMetaTags();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta Debbarma'];
    $education = [];
    $statistics = [];
    $seo = ['title' => 'About - Dr. Jayanta <PERSON>'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?> - About</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e67e22;
            --accent-light: #f39c12;
            --success-color: #27ae60;
            --text-color: #2c3e50;
            --text-light: #7f8c8d;
            --light-gray: #ecf0f1;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            --gradient-accent: linear-gradient(45deg, #e67e22, #f39c12);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 100px 0 50px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .back-btn {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Main Content */
        .main-content {
            padding: 80px 0;
        }
        
        .about-detailed {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 60px;
            margin-bottom: 80px;
        }
        
        .about-image {
            text-align: center;
        }
        
        .about-image img {
            width: 100%;
            max-width: 400px;
            border-radius: 20px;
            box-shadow: var(--shadow-medium);
        }
        
        .about-text h2 {
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 2.2rem;
        }
        
        .about-text p {
            margin-bottom: 25px;
            font-size: 1.1rem;
            line-height: 1.8;
        }
        
        .education-section, .skills-section {
            margin-bottom: 80px;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 50px;
        }
        
        .education-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }
        
        .education-card {
            background: var(--white);
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--shadow-light);
            border-left: 5px solid var(--accent-color);
        }
        
        .education-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .education-card .institution {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .education-card .year {
            color: var(--text-light);
            margin-bottom: 15px;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .skill-category {
            background: var(--white);
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--shadow-light);
            text-align: center;
        }
        
        .skill-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: var(--white);
        }
        
        .skill-category h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .skill-list {
            list-style: none;
        }
        
        .skill-list li {
            padding: 5px 0;
            color: var(--text-light);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .about-detailed {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .back-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-flex;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Main Page
        </a>
        <div class="container">
            <h1>About <?php echo escape($personal['name'] ?? 'Dr. Jayanta Debbarma'); ?></h1>
            <p>Learn more about my background, education, and expertise</p>
            <div style="margin-top: 30px;">
                <a href="journey-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Journey</a>
                <a href="publications-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Publications</a>
                <a href="thesis-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Thesis</a>
                <a href="advisors-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Advisors</a>
                <a href="gallery-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Gallery</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Detailed About Section -->
            <div class="about-detailed" data-aos="fade-up">
                <div class="about-image">
                    <?php 
                    $aboutImagePath = 'assets/images/profile/profile_about.jpg';
                    if (!empty($personal['about_image']) && file_exists($personal['about_image'])): 
                    ?>
                        <img src="<?php echo escape($personal['about_image']); ?>" alt="<?php echo escape($personal['name']); ?>">
                    <?php elseif (file_exists($aboutImagePath)): ?>
                        <img src="<?php echo escape($aboutImagePath); ?>" alt="<?php echo escape($personal['name']); ?>">
                    <?php else: ?>
                        <img src="https://picsum.photos/seed/engineer/400/500.jpg" alt="<?php echo escape($personal['name']); ?>">
                    <?php endif; ?>
                </div>
                <div class="about-text">
                    <h2>Professional Background</h2>
                    <?php if (!empty($personal['bio']['long'])): ?>
                        <?php 
                        $bioParagraphs = explode("\n\n", $personal['bio']['long']);
                        foreach ($bioParagraphs as $paragraph): 
                        ?>
                            <p><?php echo escape(trim($paragraph)); ?></p>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>With over <?php echo escape($statistics['experience_years'] ?? 30); ?> years of experience in engineering and research, I have dedicated my career to pushing the boundaries of innovation and technology. My work spans across multiple disciplines, including mechanical engineering, materials science, and sustainable technology development.</p>
                        <p>Throughout my career, I have had the privilege of leading groundbreaking research projects, mentoring the next generation of engineers, and collaborating with industry leaders to develop solutions that address real-world challenges.</p>
                        <p>My research interests include advanced materials, sustainable engineering practices, and the integration of emerging technologies in traditional engineering applications. I believe in the power of interdisciplinary collaboration and have worked extensively with teams across various fields to drive innovation.</p>
                        <p>As an educator and researcher, I am committed to fostering the next generation of engineers and scientists. I have supervised numerous graduate students and have been involved in curriculum development to ensure that engineering education remains relevant and forward-thinking.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Education Section -->
            <div class="education-section" data-aos="fade-up">
                <h2 class="section-title">Education</h2>
                <div class="education-grid">
                    <?php if (!empty($education)): ?>
                        <?php foreach (['phd', 'masters', 'bachelors'] as $level): ?>
                            <?php if (!empty($education[$level])): ?>
                                <div class="education-card" data-aos="zoom-in">
                                    <h3><?php echo escape($education[$level]['degree'] ?? ucfirst($level)); ?></h3>
                                    <div class="institution"><?php echo escape($education[$level]['institution'] ?? 'University'); ?></div>
                                    <div class="year"><?php echo escape($education[$level]['year'] ?? 'Year'); ?></div>
                                    <p><?php echo escape($education[$level]['description'] ?? 'Advanced studies in engineering and research.'); ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="education-card" data-aos="zoom-in">
                            <h3>Ph.D. in Engineering</h3>
                            <div class="institution">Massachusetts Institute of Technology</div>
                            <div class="year">1991-1995</div>
                            <p>Doctoral research focused on advanced materials and their applications in sustainable engineering solutions.</p>
                        </div>
                        <div class="education-card" data-aos="zoom-in" data-aos-delay="100">
                            <h3>Master of Science</h3>
                            <div class="institution">Stanford University</div>
                            <div class="year">1989-1991</div>
                            <p>Specialized in materials science and engineering with focus on innovative material development.</p>
                        </div>
                        <div class="education-card" data-aos="zoom-in" data-aos-delay="200">
                            <h3>Bachelor of Engineering</h3>
                            <div class="institution">University of California, Berkeley</div>
                            <div class="year">1985-1989</div>
                            <p>Mechanical Engineering with honors, foundation in engineering principles and design.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Skills Section -->
            <div class="skills-section" data-aos="fade-up">
                <h2 class="section-title">Areas of Expertise</h2>
                <div class="skills-grid">
                    <div class="skill-category" data-aos="zoom-in">
                        <div class="skill-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3>Engineering Design</h3>
                        <ul class="skill-list">
                            <li>Mechanical Engineering</li>
                            <li>Product Development</li>
                            <li>System Integration</li>
                            <li>CAD/CAM Systems</li>
                        </ul>
                    </div>
                    <div class="skill-category" data-aos="zoom-in" data-aos-delay="100">
                        <div class="skill-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3>Research & Development</h3>
                        <ul class="skill-list">
                            <li>Materials Science</li>
                            <li>Innovation Management</li>
                            <li>Technology Transfer</li>
                            <li>Patent Development</li>
                        </ul>
                    </div>
                    <div class="skill-category" data-aos="zoom-in" data-aos-delay="200">
                        <div class="skill-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3>Sustainable Engineering</h3>
                        <ul class="skill-list">
                            <li>Green Technology</li>
                            <li>Environmental Impact</li>
                            <li>Renewable Energy</li>
                            <li>Lifecycle Assessment</li>
                        </ul>
                    </div>
                    <div class="skill-category" data-aos="zoom-in" data-aos-delay="300">
                        <div class="skill-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>Leadership & Management</h3>
                        <ul class="skill-list">
                            <li>Team Leadership</li>
                            <li>Project Management</li>
                            <li>Strategic Planning</li>
                            <li>Mentoring</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
