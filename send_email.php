<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include PHPMailer files
require 'PHPMailer/PHPMailer.php';
require 'PHPMailer/SMTP.php';
require 'PHPMailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Only process POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get POST data
$raw_input = file_get_contents('php://input');
$input = json_decode($raw_input, true);

// Validate input
if (!$input) {
    // Fallback to form data
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message = $_POST['message'] ?? '';
} else {
    $name = $input['name'] ?? '';
    $email = $input['email'] ?? '';
    $subject = $input['subject'] ?? '';
    $message = $input['message'] ?? '';
}

// Validate required fields
if (empty($name) || empty($email) || empty($subject) || empty($message)) {
    echo json_encode(['success' => false, 'error' => 'All fields are required']);
    exit;
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'error' => 'Invalid email format']);
    exit;
}

// Sanitize inputs
$name = htmlspecialchars(trim($name));
$email = htmlspecialchars(trim($email));
$subject = htmlspecialchars(trim($subject));
$message = htmlspecialchars(trim($message));

try {
    // Create PHPMailer instance
    $mail = new PHPMailer(true);

    // SMTP Server Configuration
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'qfgvxbfdrzkpmvjy'; // App Password for Gmail
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = 465;

    // Additional SMTP options for better compatibility
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );

    // Email Configuration
    $mail->setFrom('<EMAIL>', 'Dr. Jayanta Debbarma - Contact Form');
    // $mail->addAddress('<EMAIL>', 'Dr. Jayanta Debbarma');
    $mail->addAddress('<EMAIL>', 'Dr. Jayanta Debbarma');
    $mail->addReplyTo($email, $name); // Set sender's email as reply-to

    // Email Content
    $mail->isHTML(true);
    $mail->Subject = 'Portfolio Contact Form: ' . $subject;
    
    // Create HTML email body
    $mail->Body = '
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1a3a5f; color: white; padding: 20px; text-align: center; }
            .content { background-color: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #1a3a5f; }
            .footer { background-color: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>New Contact Form Submission</h2>
            </div>
            <div class="content">
                <div class="field">
                    <span class="label">Name:</span><br>
                    ' . $name . '
                </div>
                <div class="field">
                    <span class="label">Email:</span><br>
                    ' . $email . '
                </div>
                <div class="field">
                    <span class="label">Subject:</span><br>
                    ' . $subject . '
                </div>
                <div class="field">
                    <span class="label">Message:</span><br>
                    ' . nl2br($message) . '
                </div>
            </div>
            <div class="footer">
                <p>This email was sent from your portfolio contact form</p>
                <p>Received on: ' . date('Y-m-d H:i:s') . '</p>
            </div>
        </div>
    </body>
    </html>';

    // Plain text version
    $mail->AltBody = "New contact form submission:\n\n";
    $mail->AltBody .= "Name: $name\n";
    $mail->AltBody .= "Email: $email\n";
    $mail->AltBody .= "Subject: $subject\n\n";
    $mail->AltBody .= "Message:\n$message\n\n";
    $mail->AltBody .= "Received on: " . date('Y-m-d H:i:s');

    // Send email
    $mail->send();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Thank you for your message! I will get back to you soon.'
    ]);

} catch (Exception $e) {
    error_log('PHPMailer Error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => 'Sorry, there was an error sending your message. Please try again later.'
    ]);
}
?>