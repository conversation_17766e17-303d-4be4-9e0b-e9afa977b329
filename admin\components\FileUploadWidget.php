<?php
/**
 * Universal File Upload Widget
 * Can be embedded anywhere in the admin system
 */

class FileUploadWidget {
    private $upload_config;
    
    public function __construct() {
        $this->upload_config = [
            'images' => [
                'path' => '../assets/images/',
                'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                'max_size' => 5 * 1024 * 1024, // 5MB
                'description' => 'Images (JPG, PNG, GIF, WebP)'
            ],
            'documents' => [
                'path' => '../assets/documents/',
                'allowed_types' => ['pdf', 'doc', 'docx'],
                'max_size' => 10 * 1024 * 1024, // 10MB
                'description' => 'Documents (PDF, DOC, DOCX)'
            ],
            'gallery' => [
                'path' => '../assets/images/gallery/',
                'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                'max_size' => 5 * 1024 * 1024, // 5MB
                'description' => 'Gallery Images'
            ],
            'thesis' => [
                'path' => '../assets/documents/thesis/',
                'allowed_types' => ['pdf'],
                'max_size' => 50 * 1024 * 1024, // 50MB
                'description' => 'Thesis Documents (PDF)'
            ],
            'publications' => [
                'path' => '../assets/documents/publications/',
                'allowed_types' => ['pdf'],
                'max_size' => 20 * 1024 * 1024, // 20MB
                'description' => 'Publication Documents (PDF)'
            ]
        ];
    }
    
    /**
     * Render the upload widget
     */
    public function render($category = 'images', $field_name = 'file_path', $current_value = '', $widget_id = null) {
        if (!$widget_id) {
            $widget_id = 'upload_widget_' . uniqid();
        }
        
        $config = $this->upload_config[$category] ?? $this->upload_config['images'];
        
        ob_start();
        ?>
        <div class="file-upload-widget" id="<?php echo $widget_id; ?>">
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               name="<?php echo htmlspecialchars($field_name); ?>" 
                               id="<?php echo $widget_id; ?>_input"
                               value="<?php echo htmlspecialchars($current_value); ?>" 
                               placeholder="File path or upload new file"
                               readonly>
                        <button type="button" 
                                class="btn btn-outline-secondary" 
                                onclick="clearFile('<?php echo $widget_id; ?>')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <small class="text-muted"><?php echo $config['description']; ?> (Max: <?php echo number_format($config['max_size'] / 1024 / 1024, 1); ?>MB)</small>
                </div>
                <div class="col-md-4">
                    <button type="button" 
                            class="btn btn-primary w-100" 
                            onclick="openUploadModal('<?php echo $widget_id; ?>', '<?php echo $category; ?>')">
                        <i class="fas fa-upload"></i> Upload File
                    </button>
                </div>
            </div>
            
            <!-- Preview Area -->
            <div id="<?php echo $widget_id; ?>_preview" class="mt-2" style="<?php echo $current_value ? '' : 'display: none;'; ?>">
                <?php if ($current_value): ?>
                    <?php echo $this->generatePreview($current_value, $category); ?>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Generate preview for uploaded file
     */
    public function generatePreview($file_path, $category) {
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        $is_image = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        
        ob_start();
        ?>
        <div class="file-preview-container p-2 border rounded">
            <div class="d-flex align-items-center gap-2">
                <?php if ($is_image): ?>
                    <img src="<?php echo htmlspecialchars($file_path); ?>" 
                         alt="Preview" 
                         class="file-preview-thumb"
                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; cursor: pointer;"
                         onclick="showImageModal('<?php echo htmlspecialchars($file_path); ?>', '<?php echo htmlspecialchars(basename($file_path)); ?>')">
                <?php else: ?>
                    <div class="file-icon-small" style="width: 50px; height: 50px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-file-<?php echo $extension === 'pdf' ? 'pdf' : 'alt'; ?>" style="color: <?php echo $extension === 'pdf' ? '#dc3545' : '#6c757d'; ?>;"></i>
                    </div>
                <?php endif; ?>
                <div class="flex-grow-1">
                    <small class="text-muted d-block"><?php echo htmlspecialchars(basename($file_path)); ?></small>
                    <small class="text-primary"><?php echo htmlspecialchars($file_path); ?></small>
                </div>
                <?php if ($is_image): ?>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showImageModal('<?php echo htmlspecialchars($file_path); ?>', '<?php echo htmlspecialchars(basename($file_path)); ?>')">
                        <i class="fas fa-eye"></i>
                    </button>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Handle file upload via AJAX
     */
    public function handleUpload() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return ['success' => false, 'message' => 'Invalid request method'];
        }
        
        // CSRF protection
        if (!isset($_POST['csrf_token']) || !Security::validateCSRFToken($_POST['csrf_token'])) {
            return ['success' => false, 'message' => 'Invalid CSRF token'];
        }
        
        $category = $_POST['category'] ?? 'images';
        $config = $this->upload_config[$category] ?? $this->upload_config['images'];
        
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'No file uploaded or upload error'];
        }
        
        $file = $_FILES['file'];
        
        // Validate file
        $validation = Security::validateFileUpload($file, $config['allowed_types'], $config['max_size']);
        if (!$validation['valid']) {
            return ['success' => false, 'message' => $validation['message']];
        }
        
        // Create directory if it doesn't exist
        if (!is_dir($config['path'])) {
            mkdir($config['path'], 0755, true);
        }
        
        // Generate secure filename
        $filename = Security::generateSecureFilename($file['name']);
        $filepath = $config['path'] . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $relative_path = str_replace(['../', '\\'], ['', '/'], $filepath);
            $preview_html = $this->generatePreview($relative_path, $category);
            return [
                'success' => true,
                'message' => 'File uploaded successfully',
                'file_path' => $relative_path,
                'filename' => $filename,
                'preview' => $preview_html
            ];
        } else {
            return ['success' => false, 'message' => 'Failed to save uploaded file'];
        }
    }
    
    /**
     * Get CSS for the widget
     */
    public static function getCSS() {
        return '
        .file-upload-widget .file-preview-thumb:hover {
            opacity: 0.8;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }
        .file-upload-widget .file-preview-container {
            background: #f8f9fa;
        }
        .file-upload-widget .file-preview-container:hover {
            background: #e9ecef;
        }
        ';
    }
    
    /**
     * Get JavaScript for the widget
     */
    public static function getJS() {
        return '
        function openUploadModal(widgetId, category) {
            const modal = document.getElementById("universalUploadModal");
            if (!modal) {
                createUploadModal();
            }
            
            // Set current widget and category
            window.currentUploadWidget = widgetId;
            window.currentUploadCategory = category;
            
            // Reset modal
            document.getElementById("modalFileInput").value = "";
            document.getElementById("modalFileInfo").style.display = "none";
            document.getElementById("modalUploadBtn").disabled = true;
            
            // Show modal
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
        
        function clearFile(widgetId) {
            document.getElementById(widgetId + "_input").value = "";
            document.getElementById(widgetId + "_preview").style.display = "none";
        }
        
        function createUploadModal() {
            const modalHTML = `
                <div class="modal fade" id="universalUploadModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Upload File</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="modalFileInput" accept="*/*">
                                </div>
                                <div id="modalFileInfo" style="display: none;" class="alert alert-info">
                                    <strong>Selected:</strong> <span id="modalFileName"></span><br>
                                    <strong>Size:</strong> <span id="modalFileSize"></span>
                                </div>
                                <div id="modalUploadProgress" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="modalUploadBtn" onclick="uploadFile()" disabled>
                                    <i class="fas fa-upload"></i> Upload
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML("beforeend", modalHTML);
            
            // Add file input change listener
            document.getElementById("modalFileInput").addEventListener("change", function(e) {
                const file = e.target.files[0];
                if (file) {
                    document.getElementById("modalFileName").textContent = file.name;
                    document.getElementById("modalFileSize").textContent = (file.size / 1024).toFixed(2) + " KB";
                    document.getElementById("modalFileInfo").style.display = "block";
                    document.getElementById("modalUploadBtn").disabled = false;
                } else {
                    document.getElementById("modalFileInfo").style.display = "none";
                    document.getElementById("modalUploadBtn").disabled = true;
                }
            });
        }
        
        function uploadFile() {
            const fileInput = document.getElementById("modalFileInput");
            const file = fileInput.files[0];
            
            if (!file) {
                alert("Please select a file");
                return;
            }
            
            const formData = new FormData();
            formData.append("file", file);
            formData.append("category", window.currentUploadCategory);
            formData.append("csrf_token", document.querySelector("input[name=csrf_token]").value);
            
            // Show progress
            document.getElementById("modalUploadProgress").style.display = "block";
            document.getElementById("modalUploadBtn").disabled = true;
            
            fetch("components/upload_handler.php", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the widget
                    const widgetInput = document.getElementById(window.currentUploadWidget + "_input");
                    const widgetPreview = document.getElementById(window.currentUploadWidget + "_preview");
                    
                    widgetInput.value = data.file_path;
                    widgetPreview.innerHTML = data.preview;
                    widgetPreview.style.display = "block";
                    
                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById("universalUploadModal")).hide();
                    
                    showToast("File uploaded successfully!", "success");
                } else {
                    alert("Upload failed: " + data.message);
                }
            })
            .catch(error => {
                console.error("Upload error:", error);
                alert("Upload failed: " + error.message);
            })
            .finally(() => {
                document.getElementById("modalUploadProgress").style.display = "none";
                document.getElementById("modalUploadBtn").disabled = false;
            });
        }
        ';
    }
}
?>
