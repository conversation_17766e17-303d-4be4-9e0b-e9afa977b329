<?php
// Start session and include required files
session_start();

// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $statistics = $dataLoader->getStatistics();
    $socialLinks = $dataLoader->getSocialLinks();
    $publications = $dataLoader->getPublications();
    $advisors = $dataLoader->getAdvisors();
    $gallery = $dataLoader->getGallery();
    $journey = $dataLoader->getJourney();
    $thesis = $dataLoader->getThesis();
    $youtubeVideos = $dataLoader->getYouTubeVideos();
    $contact = $dataLoader->getContact();
    $seo = $dataLoader->getMetaTags();
    $nameFormatted = $dataLoader->getFormattedName();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta Debbarma', 'title' => 'Distinguished Engineer'];
    $statistics = ['experience_years' => 30, 'projects_completed' => 150, 'publications' => 50];
    $socialLinks = [];
    $publications = [];
    $advisors = [];
    $gallery = [];
    $journey = [];
    $thesis = [];
    $youtubeVideos = [];
    $contact = [];
    $seo = ['title' => 'Dr. Jayanta Debbarma - Portfolio', 'description' => 'Portfolio website'];
    $nameFormatted = ['display' => 'Dr. Jayanta Debbarma'];
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?></title>
    <meta name="description" content="<?php echo escape($seo['description']); ?>">
    <?php if (!empty($seo['keywords'])): ?>
        <meta name="keywords" content="<?php echo escape($seo['keywords']); ?>">
    <?php endif; ?>
    <meta name="author" content="<?php echo escape($seo['author']); ?>">
    <link rel="icon" type="image/png" href="assets/icons/fabicon.png">
    <link rel="shortcut icon" type="image/png" href="assets/icons/fabicon.png">
    <link rel="apple-touch-icon" href="assets/icons/fabicon.png">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Responsive CSS -->
    <link rel="stylesheet" href="assets/css/responsive.css">

    <style>
        :root {
            --primary-color: #0277ecff;
            --secondary-color: #003d7aff;
            --accent-color: #e67e22;
            --accent-light: #f39c12;
            --success-color: #27ae60;
            --info-color: #3498db;
            --warning-color: #f1c40f;
            --danger-color: #e74c3c;
            --text-color: #2c3e50;
            --text-light: #7f8c8d;
            --light-gray: #ecf0f1;
            --medium-gray: #bdc3c7;
            --dark-gray: #34495e;
            --white: #ffffff;
            --section-padding: 80px 0;
            --gradient-primary: linear-gradient(135deg, #2391ffff 0%, #0080ffff 50%, #00356aff 100%);
            --gradient-accent: linear-gradient(45deg, #e67e22, #f39c12);
            --gradient-success: linear-gradient(45deg, #27ae60, #2ecc71);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 10px 40px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            padding: var(--section-padding);
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
        }

        h2 {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .view-more-section {
            text-align: center;
            margin-top: 50px;
        }

        .view-more-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--gradient-accent);
            color: var(--white);
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-medium);
        }

        .view-more-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 30px rgba(0, 0, 0, 0.15);
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-brand .logo-icon {
            width: 45px;
            height: 45px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 20px;
        }

        .nav-brand h3 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-color);
            font-weight: 500;
            font-size: 0.95rem;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--accent-color);
            background: rgba(230, 126, 34, 0.1);
        }

        .nav-links a.active {
            color: var(--white);
            background: var(--gradient-accent);
        }

        .nav-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
        }

        .nav-toggle span {
            width: 25px;
            height: 3px;
            background: var(--primary-color);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .nav-toggle.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .nav-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .nav-toggle.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Mobile Navigation */
        @media (max-width: 768px) {
            .nav-toggle {
                display: flex;
            }

            .nav-links {
                position: fixed;
                top: 100%;
                left: 0;
                width: 100%;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                flex-direction: column;
                gap: 0;
                padding: 20px 0;
                box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
                transform: translateY(-100vh);
                transition: transform 0.3s ease;
            }

            .nav-links.active {
                transform: translateY(0);
            }

            .nav-links a {
                padding: 15px 30px;
                width: 100%;
                text-align: center;
                border-radius: 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }

            .nav-links a:last-child {
                border-bottom: none;
            }
        }

        /* Landing Section */
        .landing {
            height: 100vh;
            display: flex;
            align-items: center;
            background: var(--gradient-primary);
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .landing::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="300" cy="700" r="120" fill="url(%23a)"/><circle cx="700" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-content {
            text-align: left;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            margin-bottom: 20px;
            animation: slideInLeft 1s ease-out;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.1;
            animation: slideInLeft 1s ease-out 0.2s both;
        }

        .hero-title .highlight {
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
            animation: slideInLeft 1s ease-out 0.4s both;
        }

        .hero-stats {
            display: flex;
            gap: 30px;
            margin-bottom: 40px;
            animation: slideInLeft 1s ease-out 0.6s both;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent-color);
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .hero-cta {
            display: flex;
            gap: 20px;
            animation: slideInLeft 1s ease-out 0.8s both;
        }

        .cta-primary,
        .cta-secondary {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .cta-primary {
            background: var(--gradient-accent);
            color: var(--white);
            box-shadow: var(--shadow-medium);
        }

        .cta-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        .cta-secondary {
            background: transparent;
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .cta-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: slideInRight 1s ease-out 0.5s both;
        }

        .profile-image {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: var(--gradient-accent);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 80px;
            color: var(--white);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            position: relative;
        }

        .profile-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .profile-image.has-image {
            background: none;
        }

        .profile-image.has-image i {
            display: none;
        }

        .profile-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .profile-title {
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .profile-social {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--accent-color);
            transform: translateY(-3px);
        }

        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: var(--white);
            text-align: center;
            animation: bounce 2s infinite;
        }

        .scroll-indicator span {
            display: block;
            font-size: 0.9rem;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .scroll-indicator i {
            font-size: 24px;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0) translateX(-50%);
            }

            40% {
                transform: translateY(-10px) translateX(-50%);
            }

            60% {
                transform: translateY(-5px) translateX(-50%);
            }
        }

        /* Additional Styles for Dynamic Content */
        .about-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 50px;
            align-items: center;
        }

        .about-image img {
            width: 100%;
            border-radius: 15px;
            box-shadow: var(--shadow-medium);
        }

        .about-text p {
            margin-bottom: 20px;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        /* Journey Section */
        .journey {
            background: var(--white);
            padding: 80px 0;
        }

        .timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--accent-color);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 50px;
            width: 50%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            padding-right: 30px;
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 30px;
        }

        .timeline-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: var(--accent-color);
            border-radius: 50%;
            top: 0;
        }

        .timeline-item:nth-child(odd) .timeline-dot {
            right: -10px;
        }

        .timeline-item:nth-child(even) .timeline-dot {
            left: -10px;
        }

        .timeline-content {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--shadow-light);
        }

        .timeline-year {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 10px;
        }

        .journal-grid,
        .advisors-grid,
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .journal-card,
        .advisor-card {
            background: var(--white);
            border-radius: 15px;
            padding: 30px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .journal-card:hover,
        .advisor-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-medium);
        }

        .journal-title {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .journal-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .journal-link {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-top: 15px;
        }

        .advisor-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 20px;
            overflow: hidden;
        }

        .advisor-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .advisor-name {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .advisor-title {
            text-align: center;
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 15px;
        }

        .advisor-bio {
            text-align: center;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .gallery-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            margin-bottom: 40px;
        }

        .gallery-tab {
            padding: 12px 24px;
            border: 2px solid var(--accent-color);
            background: transparent;
            color: var(--accent-color);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .gallery-tab.active,
        .gallery-tab:hover {
            background: var(--accent-color);
            color: var(--white);
        }

        .gallery-tab-content {
            display: none;
        }

        .gallery-tab-content.active {
            display: block;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--white);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            height: 250px;
        }

        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .gallery-item-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            color: var(--white);
            padding: 20px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .gallery-item:hover .gallery-item-overlay {
            transform: translateY(0);
        }

        .gallery-item-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .gallery-item-description {
            font-size: 0.9rem;
            line-height: 1.4;
            margin: 0 0 10px 0;
            opacity: 0.9;
        }

        .gallery-item-category {
            display: inline-block;
            background: var(--accent-color);
            color: var(--white);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .gallery-type-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: var(--white);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .gallery-item:hover .gallery-type-icon {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        .message-form {
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .submit-btn {
            background: var(--gradient-accent);
            color: var(--white);
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0 auto;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .form-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .form-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .form-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        footer {
            background: var(--primary-color);
            color: var(--white);
            padding: 50px 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }

        .footer-column h3 {
            margin-bottom: 20px;
            color: var(--accent-color);
        }

        .footer-column ul {
            list-style: none;
        }

        .footer-column ul li {
            margin-bottom: 10px;
        }

        .footer-column ul li a {
            color: var(--white);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-column ul li a:hover {
            color: var(--accent-color);
        }

        .contact-info p {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footer-social {
            display: flex;
            gap: 15px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Fixed Corner Visitor Counter */
        .visitor-counter-fixed {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(15px);
            color: var(--white);
            padding: 12px 18px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .visitor-counter-fixed:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            background: rgba(44, 62, 80, 1);
        }

        .visitor-counter-fixed i {
            margin-right: 8px;
            color: var(--accent-color);
        }

        #visitor-count {
            color: var(--accent-color);
            font-weight: 700;
            margin-left: 5px;
        }

        /* Hide old visitor counter in profile */
        .profile-card .visitor-counter {
            display: none;
        }

        /* Mobile responsive for corner counter */
        @media (max-width: 768px) {
            .visitor-counter-fixed {
                top: 15px;
                right: 15px;
                padding: 10px 15px;
                font-size: 0.8rem;
            }
        }

        /* Modern Thesis Cards Section */
        .thesis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 50px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .thesis-card {
            background: var(--white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            position: relative;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .thesis-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .thesis-card-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 25px;
            position: relative;
            overflow: hidden;
        }

        .thesis-card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
        }

        .thesis-card:hover .thesis-card-header::before {
            animation: shine 0.6s ease-in-out;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }

            100% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        .university-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.15);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .thesis-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .thesis-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            font-style: italic;
        }

        .thesis-card-body {
            padding: 25px;
        }

        .thesis-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--light-gray);
        }

        .thesis-author {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .thesis-year {
            background: var(--gradient-accent);
            color: var(--white);
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.85rem;
        }

        .thesis-degree {
            background: var(--success-color);
            color: var(--white);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 8px;
        }

        .thesis-abstract {
            color: var(--text-color);
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .thesis-supervisor {
            background: var(--light-gray);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid var(--accent-color);
        }

        .thesis-supervisor strong {
            color: var(--primary-color);
        }

        .thesis-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .thesis-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .thesis-btn-primary {
            background: var(--gradient-accent);
            color: var(--white);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
        }

        .thesis-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(230, 126, 34, 0.4);
        }

        .thesis-btn-secondary {
            background: var(--white);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .thesis-btn-secondary:hover {
            background: var(--primary-color);
            color: var(--white);
        }

        /* Responsive Design for Thesis Cards */
        @media (max-width: 768px) {
            .thesis-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                margin-top: 30px;
            }

            .thesis-card-header {
                padding: 20px;
            }

            .thesis-title {
                font-size: 1.2rem;
            }

            .thesis-card-body {
                padding: 20px;
            }

            .thesis-actions {
                flex-direction: column;
            }

            .thesis-btn {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .thesis-meta {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .thesis-year {
                align-self: flex-end;
            }
        }



        /* View More Section */
        .view-more-section {
            text-align: center;
            margin-top: 50px;
        }

        .view-more-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--gradient-accent);
            color: var(--white);
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            margin-top: 20px;
        }

        .view-more-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(230, 126, 34, 0.4);
        }

        .view-more-btn i {
            transition: transform 0.3s ease;
        }

        .view-more-btn:hover i {
            transform: translateX(5px);
        }

        /* YouTube Videos Section */
        .videos {
            background: var(--light-bg);
            padding: 80px 0;
        }

        .video-slider-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 60px;
            overflow: visible;
        }

        .video-slider-wrapper {
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .video-slider {
            display: flex;
            transition: transform 0.5s ease;
            gap: 20px;
        }

        .video-card {
            min-width: 350px;
            background: var(--white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.1);
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .video-card:hover .play-button {
            background: var(--primary-color);
            color: var(--white);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: var(--white);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .video-content {
            padding: 20px;
        }

        .video-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .video-description {
            color: var(--text-color);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .video-category {
            display: inline-block;
            background: var(--gradient-accent);
            color: var(--white);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .slider-nav {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            padding: 0;
            pointer-events: none;
            transform: translateY(-50%);
        }

        .slider-btn {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
            pointer-events: all;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .slider-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: scale(1.1);
        }

        .slider-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .slider-dots {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(44, 62, 80, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dot.active {
            background: var(--primary-color);
            transform: scale(1.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                justify-content: center;
            }

            .about-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .timeline::before {
                left: 20px;
            }

            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 50px !important;
                padding-right: 0 !important;
                text-align: left !important;
            }

            .timeline-dot {
                left: 10px !important;
                right: auto !important;
            }

            /* Video Slider Mobile */
            .video-card {
                min-width: 280px;
            }

            .video-thumbnail {
                height: 160px;
            }

            .play-button {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .video-content {
                padding: 15px;
            }

            .video-title {
                font-size: 1.1rem;
            }

            .slider-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .slider-nav {
                padding: 0;
            }

            .video-slider-container {
                padding: 0 50px;
            }
        }
    </style>
</head>

<body>
    <!-- Fixed Corner Visitor Counter -->
    <div class="visitor-counter-fixed">
        <i class="fas fa-eye"></i>
        <span>Visitors:</span>
        <span id="visitor-count">Loading...</span>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <div class="logo-icon">
                        <img src="assets/icons/fabicon.png" alt="Logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 50%;">
                    </div>
                    <h3><?php echo escape($personal['name'] ?? 'Dr. Jayanta Debbarma'); ?></h3>
                </div>
                <div class="nav-links" id="nav-links">
                    <a href="#home" class="nav-link active">Home</a>
                    <a href="#about" class="nav-link">About</a>
                    <a href="#journey" class="nav-link">Journey</a>
                    <a href="#publications" class="nav-link">Publications</a>
                    <a href="#gallery" class="nav-link">Gallery</a>
                    <a href="#videos" class="nav-link">Videos</a>
                    <a href="#contact" class="nav-link">Contact</a>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Landing Section -->
    <section id="home" class="landing">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i> <?php echo escape($personal['title'] ?? 'Distinguished Engineer'); ?>
                </div>
                <h1 class="hero-title">
                    <?php echo $nameFormatted['display']; ?>
                </h1>
                <p class="hero-subtitle">
                    <?php echo escape($personal['bio']['short'] ?? 'Senior Engineer with 30+ Years of Experience in Innovation and Research.'); ?>
                </p>
                <div class="hero-stats">
                    <?php echo renderStatistics($statistics); ?>
                </div>
                <div class="hero-cta">
                    <a href="#about" class="cta-primary">
                        <span>Learn More</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="#contact" class="cta-secondary">
                        <span>Get In Touch</span>
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="profile-card">
                    <div class="profile-image" id="profile-image">
                        <?php
                        $profileImagePath = 'assets/images/profile/profile_main.jpg';
                        if (!empty($personal['profile_image']) && file_exists($personal['profile_image'])):
                        ?>
                            <img src="<?php echo escape($personal['profile_image']); ?>" alt="<?php echo escape($personal['name']); ?>">
                        <?php elseif (file_exists($profileImagePath)): ?>
                            <img src="<?php echo escape($profileImagePath); ?>" alt="<?php echo escape($personal['name']); ?>">
                        <?php else: ?>
                            <i class="fas fa-user-graduate"></i>
                        <?php endif; ?>
                    </div>
                    <h3 class="profile-name"><?php echo escape($personal['name'] ?? 'Dr. Jayanta Debbarma'); ?></h3>
                    <p class="profile-title"><?php echo escape($personal['title'] ?? 'Distinguished Engineer & Researcher'); ?></p>
                    <div class="profile-social">
                        <?php echo renderSocialLinks($socialLinks); ?>
                    </div>
                    <div class="visitor-counter">
                        <span>Visitors: <span id="visitor-count">Loading...</span></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <span>Scroll Down</span>
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about section" data-aos="fade-up">
        <div class="container">
            <h2>About Me</h2>
            <div class="about-content">
                <div class="about-image" data-aos="fade-right">
                    <?php
                    $src = 'https://picsum.photos/seed/engineer/500/500.jpg'; // default fallback

                    if (!empty($personal['about_image'])) {
                        $src = $personal['about_image'];
                    } elseif (file_exists(__DIR__ . '/assets/images/profile/profile_about.jpg')) {
                        $src = 'assets/images/profile/profile_about.jpg';
                    }
                    ?>
                    <img
                        src="<?php echo escape($src); ?>"
                        alt="<?php echo escape($personal['name'] ?? 'Dr. Jayanta Debbarma'); ?>"
                        onerror="this.onerror=null; this.src='https://picsum.photos/seed/engineer/500/500.jpg';">
                </div>
                <div class="about-text" data-aos="fade-left">
                    <?php if (!empty($personal['bio']['long'])): ?>
                        <?php
                        $bioParagraphs = explode("\n\n", $personal['bio']['long']);
                        foreach ($bioParagraphs as $paragraph):
                        ?>
                            <p><?php echo escape(trim($paragraph)); ?></p>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>With over <?php echo escape($statistics['experience_years'] ?? 30); ?> years of experience in engineering and research, I, Dr. Debbarma, a distinguished groundwater researcher from Tripura, India, have dedicated my career to specializing in sustainable water management. My expertise lies in groundwater potential mapping, spring shed management, and spring identification, utilizing advanced methodologies such as the Analytical Hierarchy Process (AHP) and Geographic Information Systems (GIS).</p>
                        <p>Throughout my career, I have significantly contributed to identifying areas with substantial groundwater resources and springs, particularly in the flood plains and intermontane synclinal troughs of Tripura, thereby aiding in the sustainable management of these vital resources. I have had the privilege of leading groundbreaking research projects, mentoring the next generation of engineers, and collaborating with industry leaders to develop solutions that address real-world challenges.</p>
                        <p>I hold a Ph.D. in <?php echo escape($personal['education']['phd']['field'] ?? 'Engineering'); ?> and have authored numerous publications in peer-reviewed journals. My commitment to excellence and innovation has been recognized through various awards and honors in the engineering community.</p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="view-more-section">
                <a href="about-more.php" class="view-more-btn">
                    <span>Learn More About Me</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Journey Section -->
    <section id="journey" class="journey section" data-aos="fade-up">
        <div class="container">
            <h2>My Journey</h2>
            <div class="timeline">
                <?php echo renderJourney($journey); ?>
            </div>
            <div class="view-more-section">
                <a href="journey-more.php" class="view-more-btn">
                    <span>View Complete Journey</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Publications Section -->
    <section id="publications" class="journals section" data-aos="fade-up">
        <div class="container">
            <h2>Journals & Papers</h2>
            <div class="journal-grid">
                <?php echo renderPublications($publications, 6); ?>
            </div>
            <?php if (shouldShowViewMore($publications, 6)): ?>
                <div class="view-more-section">
                    <a href="publications-more.php" class="view-more-btn">
                        <span>View All Publications</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Thesis Section -->
    <section id="thesis" class="thesis section" data-aos="fade-up">
        <div class="container">
            <h2>Thesis & Research</h2>
            <p class="section-subtitle">Explore my academic research and scholarly contributions</p>
            <div class="thesis-grid">
                <?php echo renderThesisBookshelf($thesis); ?>
            </div>
            <div class="view-more-section">
                <a href="thesis-more.php" class="view-more-btn">
                    <span>View All Research</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Advisors Section -->
    <?php if (!empty($advisors)): ?>
        <section class="advisors section" data-aos="fade-up">
            <div class="container">
                <h2>Key Advisors</h2>
                <div class="advisors-grid">
                    <?php echo renderAdvisors($advisors, 4); ?>
                </div>
                <?php if (shouldShowViewMore($advisors, 4)): ?>
                    <div class="view-more-section">
                        <a href="advisors-more.php" class="view-more-btn">
                            <span>Meet All Advisors</span>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    <?php endif; ?>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery section" data-aos="fade-up">
        <div class="container">
            <h2>Field of Interest</h2>
            <div class="gallery-tabs">
                <button class="gallery-tab active" data-tab="images">Images</button>
                <button class="gallery-tab" data-tab="all">All Items</button>
                <button class="gallery-tab" data-tab="research">Research</button>
                <button class="gallery-tab" data-tab="projects">Projects</button>
                <button class="gallery-tab" data-tab="documents">Documents</button>
                <button class="gallery-tab" data-tab="videos">Videos</button>
            </div>

            <div class="gallery-tab-content active" id="images">
                <div class="gallery-grid">
                    <?php
                    // Filter for image type items
                    $imageItems = array_filter($gallery, function ($item) {
                        return isset($item['type']) && $item['type'] === 'image';
                    });
                    echo renderGallery($imageItems, null, 6);
                    ?>
                </div>
                <?php if (shouldShowViewMore($imageItems, 6)): ?>
                    <div class="view-more">
                        <a href="gallery-more.php" class="view-more-btn">View More Images</a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="gallery-tab-content" id="all">
                <div class="gallery-grid">
                    <?php echo renderGallery($gallery, null, 6); ?>
                </div>
                <?php if (shouldShowViewMore($gallery, 6)): ?>
                    <div class="view-more">
                        <a href="gallery-more.php" class="view-more-btn">View Complete Gallery</a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="gallery-tab-content" id="research">
                <div class="gallery-grid">
                    <?php
                    $researchItems = array_filter($gallery, function ($item) {
                        return isset($item['category']) && $item['category'] === 'research';
                    });
                    echo renderGallery($researchItems, null, 6);
                    ?>
                </div>
                <?php if (shouldShowViewMore($researchItems, 6)): ?>
                    <div class="view-more">
                        <a href="gallery-more.php" class="view-more-btn">View More Research</a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="gallery-tab-content" id="projects">
                <div class="gallery-grid">
                    <?php
                    $projectItems = array_filter($gallery, function ($item) {
                        return isset($item['category']) && $item['category'] === 'projects';
                    });
                    echo renderGallery($projectItems, null, 6);
                    ?>
                </div>
                <?php if (shouldShowViewMore($projectItems, 6)): ?>
                    <div class="view-more">
                        <a href="gallery-more.php" class="view-more-btn">View More Projects</a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="gallery-tab-content" id="documents">
                <div class="gallery-grid">
                    <?php
                    $documentItems = array_filter($gallery, function ($item) {
                        return isset($item['category']) && $item['category'] === 'documents';
                    });
                    echo renderGallery($documentItems, null, 6);
                    ?>
                </div>
                <?php if (shouldShowViewMore($documentItems, 6)): ?>
                    <div class="view-more">
                        <a href="gallery-more.php" class="view-more-btn">View More Documents</a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="gallery-tab-content" id="videos">
                <div class="gallery-grid">
                    <?php
                    $videoItems = array_filter($gallery, function ($item) {
                        return isset($item['category']) && $item['category'] === 'videos';
                    });
                    echo renderGallery($videoItems, null, 6);
                    ?>
                </div>
                <?php if (shouldShowViewMore($videoItems, 6)): ?>
                    <div class="view-more">
                        <a href="gallery-more.php" class="view-more-btn">View More Videos</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- YouTube Videos Section -->
    <section id="videos" class="videos section" data-aos="fade-up">
        <div class="container">
            <h2>Research Videos</h2>
            <p class="section-subtitle">Watch my research presentations and educational content</p>

            <div class="video-slider-container">
                <div class="video-slider-wrapper">
                    <div class="video-slider" id="videoSlider">
                        <?php echo renderYouTubeVideos($youtubeVideos, 6); ?>
                    </div>
                </div>

                <!-- Slider Navigation -->
                <div class="slider-nav">
                    <button class="slider-btn prev-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="slider-btn next-btn" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- Slider Dots -->
                <div class="slider-dots" id="sliderDots"></div>
            </div>

            <!-- <?php if (shouldShowViewMore($youtubeVideos, 6)): ?>
            <div class="view-more-section">
                <a href="#" class="view-more-btn">
                    <span>View All Videos</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <?php endif; ?> -->

            <!-- <div class="view-more-section">
                <a href="videos-more.php" class="view-more-btn">
                    <span>View All Videos</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div> -->
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="message section" data-aos="fade-up">
        <div class="container">
            <h2>Get In Touch</h2>
            <form class="message-form" id="contact-form">
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" rows="5" required></textarea>
                </div>
                <button type="submit" class="submit-btn">
                    <span>Send Message</span>
                    <i class="fas fa-paper-plane"></i>
                </button>
                <div id="form-message" class="form-message" style="display: none;"></div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer data-aos="fade-up">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>About Me</h3>
                    <p><?php echo escape($personal['bio']['short'] ?? 'Senior engineer with 30+ years of experience in innovation and research. Passionate about developing sustainable engineering solutions for a better future.'); ?></p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#about">About</a></li>
                        <li><a href="#journey">Journey</a></li>
                        <li><a href="#publications">Publications</a></li>
                        <li><a href="#gallery">Gallery</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Info</h3>
                    <div class="contact-info">
                        <?php if (!empty($contact['email'])): ?>
                            <p><i class="fas fa-envelope"></i> <?php echo escape($contact['email']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($contact['phone'])): ?>
                            <p><i class="fas fa-phone"></i> <?php echo escape($contact['phone']); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($contact['address'])): ?>
                            <p><i class="fas fa-map-marker-alt"></i> <?php echo escape($contact['address']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Follow Me</h3>
                    <div class="footer-social">
                        <?php echo renderSocialLinks($socialLinks); ?>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo escape($personal['name'] ?? 'Dr. Jayanta Debbarma'); ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Visitor Counter - Load from database
        function loadVisitorCount() {
            const visitorElement = document.getElementById('visitor-count');
            if (!visitorElement) {
                console.error('Visitor count element not found');
                return;
            }

            fetch('visitor_counter.php', {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Visitor counter response:', data);
                    if (data.success) {
                        const count = data.total_visits || data.count || '0';
                        visitorElement.textContent = count;
                        console.log('Visitor count updated to:', count);
                    } else {
                        throw new Error(data.error || 'Unknown error');
                    }
                })
                .catch(error => {
                    console.error('Error fetching visitor count:', error);
                    visitorElement.textContent = 'Error';
                    // Try again after 5 seconds
                    setTimeout(loadVisitorCount, 5000);
                });
        }

        // Load visitor count when page loads
        loadVisitorCount();

        // Navigation functionality
        const navbar = document.getElementById('navbar');
        const navToggle = document.getElementById('nav-toggle');
        const navLinks = document.getElementById('nav-links');
        const navLinkElements = document.querySelectorAll('.nav-link');

        // Mobile menu toggle
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navLinks.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        navLinkElements.forEach(link => {
            link.addEventListener('click', function() {
                navToggle.classList.remove('active');
                navLinks.classList.remove('active');
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Active navigation link highlighting
        function updateActiveNavLink() {
            const sections = document.querySelectorAll('section[id]');
            const scrollPos = window.scrollY + 100;

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                const sectionId = section.getAttribute('id');

                if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                    navLinkElements.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === `#${sectionId}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        }

        window.addEventListener('scroll', updateActiveNavLink);

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Gallery Tab Switching
        document.querySelectorAll('.gallery-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                document.querySelectorAll('.gallery-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.gallery-tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const targetId = this.getAttribute('data-tab');
                document.getElementById(targetId).classList.add('active');
            });
        });



        // Contact Form Submission
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const formMessage = document.getElementById('form-message');
            const submitBtn = this.querySelector('.submit-btn');

            // Show loading state
            submitBtn.innerHTML = '<span>Sending...</span><i class="fas fa-spinner fa-spin"></i>';
            submitBtn.disabled = true;

            // Convert FormData to JSON
            const jsonData = {};
            formData.forEach((value, key) => {
                jsonData[key] = value;
            });

            fetch('send_email.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(jsonData)
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button
                    submitBtn.innerHTML = '<span>Send Message</span><i class="fas fa-paper-plane"></i>';
                    submitBtn.disabled = false;

                    if (data.success) {
                        formMessage.textContent = data.message || 'Thank you for your message! I will get back to you soon.';
                        formMessage.className = 'form-message success';
                        formMessage.style.display = 'block';

                        // Reset form
                        this.reset();
                    } else {
                        throw new Error(data.error || 'Unknown error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Reset button
                    submitBtn.innerHTML = '<span>Send Message</span><i class="fas fa-paper-plane"></i>';
                    submitBtn.disabled = false;

                    formMessage.textContent = 'An error occurred while sending your message. Please try again.';
                    formMessage.className = 'form-message error';
                    formMessage.style.display = 'block';
                });

            // Hide message after 5 seconds
            setTimeout(() => {
                formMessage.style.display = 'none';
            }, 5000);
        });

        // Gallery item click handler - now handled by onclick in HTML

        // Profile image handling
        const profileImage = document.getElementById('profile-image');
        if (profileImage) {
            const img = profileImage.querySelector('img');
            if (img) {
                img.onload = function() {
                    profileImage.classList.add('has-image');
                };
                img.onerror = function() {
                    // If image fails to load, show icon instead
                    profileImage.innerHTML = '<i class="fas fa-user-graduate"></i>';
                    profileImage.classList.remove('has-image');
                };
            }
        }

        // YouTube Video Slider Functionality
        function initVideoSlider() {
            const slider = document.getElementById('videoSlider');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const dotsContainer = document.getElementById('sliderDots');

            if (!slider) return;

            const cards = slider.querySelectorAll('.video-card');
            const cardWidth = 370; // 350px + 20px gap
            let currentIndex = 0;
            const maxIndex = Math.max(0, cards.length - Math.floor(slider.parentElement.offsetWidth / cardWidth));

            // Create dots
            for (let i = 0; i <= maxIndex; i++) {
                const dot = document.createElement('div');
                dot.className = 'dot';
                if (i === 0) dot.classList.add('active');
                dot.addEventListener('click', () => goToSlide(i));
                dotsContainer.appendChild(dot);
            }

            function updateSlider() {
                const translateX = -currentIndex * cardWidth;
                slider.style.transform = `translateX(${translateX}px)`;

                // Update dots
                document.querySelectorAll('.dot').forEach((dot, index) => {
                    dot.classList.toggle('active', index === currentIndex);
                });

                // Update button states
                prevBtn.disabled = currentIndex === 0;
                nextBtn.disabled = currentIndex === maxIndex;
            }

            function goToSlide(index) {
                currentIndex = Math.max(0, Math.min(index, maxIndex));
                updateSlider();
            }

            function nextSlide() {
                if (currentIndex < maxIndex) {
                    currentIndex++;
                    updateSlider();
                }
            }

            function prevSlide() {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateSlider();
                }
            }

            // Event listeners
            if (prevBtn) prevBtn.addEventListener('click', prevSlide);
            if (nextBtn) nextBtn.addEventListener('click', nextSlide);

            // Auto-slide (optional)
            setInterval(() => {
                if (currentIndex === maxIndex) {
                    currentIndex = 0;
                } else {
                    currentIndex++;
                }
                updateSlider();
            }, 5000);

            // Initial update
            updateSlider();
        }

        // Open YouTube video in modal or new tab
        function openYouTubeVideo(videoId) {
            if (!videoId) return;

            // Option 1: Open in new tab
            window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank');

            // Option 2: Create modal (uncomment if you prefer modal)
            /*
            const modal = document.createElement('div');
            modal.className = 'video-modal';
            modal.innerHTML = `
                <div class="video-modal-content">
                    <span class="video-modal-close">&times;</span>
                    <iframe width="800" height="450"
                            src="https://www.youtube.com/embed/${videoId}?autoplay=1"
                            frameborder="0" allowfullscreen></iframe>
                </div>
            `;
            document.body.appendChild(modal);

            modal.querySelector('.video-modal-close').onclick = () => {
                document.body.removeChild(modal);
            };

            modal.onclick = (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            };
            */
        }

        // Initialize video slider when page loads
        initVideoSlider();

        // Gallery item handler
        function openGalleryItem(link, type) {
            if (!link || link === '#') return;

            switch (type) {
                case 'video':
                    // Open video in new tab or create modal
                    window.open(link, '_blank');
                    break;
                case 'pdf':
                    // Open PDF in new tab
                    window.open(link, '_blank');
                    break;
                case 'image':
                default:
                    // Open image in new tab or create lightbox
                    window.open(link, '_blank');
                    break;
            }
        }

        // Make function globally available
        window.openGalleryItem = openGalleryItem;
    </script>
</body>

</html>