/* Responsive Design with Viewport Units */
/* This CSS file improves responsiveness across different screen sizes */

:root {
    /* Responsive font sizes using clamp() for better scaling */
    --font-size-xs: clamp(0.75rem, 1.5vw, 0.875rem);
    --font-size-sm: clamp(0.875rem, 2vw, 1rem);
    --font-size-base: clamp(1rem, 2.5vw, 1.125rem);
    --font-size-lg: clamp(1.125rem, 3vw, 1.25rem);
    --font-size-xl: clamp(1.25rem, 3.5vw, 1.5rem);
    --font-size-2xl: clamp(1.5rem, 4vw, 2rem);
    --font-size-3xl: clamp(2rem, 5vw, 3rem);
    --font-size-4xl: clamp(2.5rem, 6vw, 4rem);
    
    /* Responsive spacing using viewport units */
    --spacing-xs: clamp(0.25rem, 1vw, 0.5rem);
    --spacing-sm: clamp(0.5rem, 2vw, 1rem);
    --spacing-md: clamp(1rem, 3vw, 1.5rem);
    --spacing-lg: clamp(1.5rem, 4vw, 2rem);
    --spacing-xl: clamp(2rem, 5vw, 3rem);
    --spacing-2xl: clamp(3rem, 6vw, 4rem);
    --spacing-3xl: clamp(4rem, 8vw, 6rem);
    
    /* Responsive container widths */
    --container-sm: min(90vw, 540px);
    --container-md: min(90vw, 720px);
    --container-lg: min(90vw, 960px);
    --container-xl: min(90vw, 1140px);
    --container-2xl: min(90vw, 1320px);
}

/* Base responsive typography */
body {
    font-size: var(--font-size-base);
    line-height: 1.6;
}

h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
}

h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
}

h4 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

h5 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

h6 {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-sm);
}

p {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-sm);
}

/* Responsive containers */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-sm {
    max-width: var(--container-sm);
}

.container-md {
    max-width: var(--container-md);
}

.container-lg {
    max-width: var(--container-lg);
}

.container-xl {
    max-width: var(--container-xl);
}

.container-2xl {
    max-width: var(--container-2xl);
}

/* Responsive sections */
section {
    padding: var(--spacing-2xl) 0;
}

/* Responsive navigation */
.navbar {
    padding: var(--spacing-sm) 0;
}

.nav-brand h3 {
    font-size: var(--font-size-xl);
}

.nav-links a {
    font-size: var(--font-size-base);
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* Responsive hero section */
.hero-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
}

/* Responsive cards */
.card {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.card-header {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.card-body {
    padding: var(--spacing-md);
}

/* Responsive buttons */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    border-radius: clamp(0.25rem, 1vw, 0.5rem);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* Responsive forms */
.form-control {
    padding: var(--spacing-sm);
    font-size: var(--font-size-base);
    border-radius: clamp(0.25rem, 1vw, 0.375rem);
}

.form-label {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

/* Responsive grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

/* Responsive columns */
@media (min-width: 576px) {
    .col-sm-1 { flex: 0 0 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; }
    .col-sm-6 { flex: 0 0 50%; }
    .col-sm-8 { flex: 0 0 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; }
    .col-sm-12 { flex: 0 0 100%; }
}

@media (min-width: 768px) {
    .col-md-1 { flex: 0 0 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; }
    .col-md-3 { flex: 0 0 25%; }
    .col-md-4 { flex: 0 0 33.333333%; }
    .col-md-6 { flex: 0 0 50%; }
    .col-md-8 { flex: 0 0 66.666667%; }
    .col-md-9 { flex: 0 0 75%; }
    .col-md-12 { flex: 0 0 100%; }
}

@media (min-width: 992px) {
    .col-lg-1 { flex: 0 0 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; }
    .col-lg-6 { flex: 0 0 50%; }
    .col-lg-8 { flex: 0 0 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; }
    .col-lg-12 { flex: 0 0 100%; }
}

/* Responsive utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }

/* Responsive spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Mobile-first responsive breakpoints */
@media (max-width: 575.98px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .nav-links {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .card {
        padding: var(--spacing-md);
    }
}

@media (max-width: 767.98px) {
    .row {
        flex-direction: column;
    }
    
    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 0 0 100%;
        margin-bottom: var(--spacing-sm);
    }
}

/* Large screen optimizations */
@media (min-width: 1400px) {
    .container {
        max-width: var(--container-2xl);
    }
}

/* High DPI screen adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn {
        border-width: 0.5px;
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    section {
        padding: var(--spacing-lg) 0;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }
}

/* Specific overrides for main website elements */
.landing {
    min-height: 100vh;
    padding: var(--spacing-2xl) 0;
}

.hero-container {
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.hero-content {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.hero-badge {
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    min-width: clamp(120px, 20vw, 200px);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: bold;
}

.stat-label {
    font-size: var(--font-size-sm);
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-primary,
.cta-secondary {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    text-decoration: none;
    border-radius: clamp(0.5rem, 2vw, 1rem);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: clamp(150px, 25vw, 200px);
    justify-content: center;
}

/* Gallery responsive improvements */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(clamp(250px, 30vw, 350px), 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
}

.gallery-item {
    aspect-ratio: 4/3;
    border-radius: clamp(0.5rem, 2vw, 1rem);
    overflow: hidden;
}

/* Publications responsive layout */
.publications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(clamp(300px, 40vw, 400px), 1fr));
    gap: var(--spacing-lg);
}

.publication-card {
    padding: var(--spacing-lg);
    border-radius: clamp(0.5rem, 2vw, 1rem);
}

/* Journey timeline responsive */
.journey-timeline {
    position: relative;
    padding: var(--spacing-xl) 0;
}

.journey-item {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    border-radius: clamp(0.5rem, 2vw, 1rem);
}

/* Video grid responsive */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(clamp(280px, 35vw, 400px), 1fr));
    gap: var(--spacing-lg);
}

.video-item {
    aspect-ratio: 16/9;
    border-radius: clamp(0.5rem, 2vw, 1rem);
    overflow: hidden;
}

/* Contact section responsive */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(clamp(300px, 45vw, 500px), 1fr));
    gap: var(--spacing-xl);
}

/* Mobile-specific overrides */
@media (max-width: 575.98px) {
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 300px;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .publications-grid {
        grid-template-columns: 1fr;
    }

    .video-grid {
        grid-template-columns: 1fr;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }
}
