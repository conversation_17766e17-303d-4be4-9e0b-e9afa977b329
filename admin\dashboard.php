<?php
session_start();

// Simple authentication (you can enhance this)
$admin_password = 'admin123'; // Change this password
$is_authenticated = isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;

if (isset($_POST['login'])) {
    if ($_POST['password'] === $admin_password) {
        $_SESSION['admin_authenticated'] = true;
        $is_authenticated = true;
    } else {
        $login_error = "Invalid password";
    }
}

if (isset($_POST['logout'])) {
    session_destroy();
    header('Location: dashboard.php');
    exit;
}

if (!$is_authenticated) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Login</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container">
            <div class="row justify-content-center mt-5">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h4>Admin Login</h4>
                        </div>
                        <div class="card-body">
                            <?php if (isset($login_error)): ?>
                                <div class="alert alert-danger"><?php echo $login_error; ?></div>
                            <?php endif; ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <button type="submit" name="login" class="btn btn-primary w-100">Login</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Load current data
$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'update_personal':
            $data['personal']['name'] = $_POST['name'];
            $data['personal']['title'] = $_POST['title'];
            $data['personal']['designation'] = $_POST['designation'];
            $data['personal']['experience_years'] = (int)$_POST['experience_years'];
            $data['personal']['specialization'] = $_POST['specialization'];
            $data['personal']['location'] = $_POST['location'];
            $data['personal']['email'] = $_POST['email'];
            $data['personal']['phone'] = $_POST['phone'];
            $data['personal']['bio']['short'] = $_POST['bio_short'];
            $data['personal']['bio']['detailed'] = $_POST['bio_detailed'];
            break;
            
        case 'update_address':
            $data['personal']['address']['line1'] = $_POST['line1'];
            $data['personal']['address']['line2'] = $_POST['line2'];
            $data['personal']['address']['city'] = $_POST['city'];
            $data['personal']['address']['state'] = $_POST['state'];
            $data['personal']['address']['country'] = $_POST['country'];
            $data['personal']['address']['pincode'] = $_POST['pincode'];
            break;
            
        case 'update_contact':
            $data['contact']['email'] = $_POST['email'];
            $data['contact']['phone'] = $_POST['phone'];
            $data['contact']['office_hours'] = $_POST['office_hours'];
            $data['contact']['response_time'] = $_POST['response_time'];
            $data['contact']['preferred_contact'] = $_POST['preferred_contact'];
            break;
            
        case 'update_statistics':
            $data['statistics']['experience_years'] = (int)$_POST['experience_years'];
            $data['statistics']['projects_completed'] = (int)$_POST['projects_completed'];
            $data['statistics']['publications'] = (int)$_POST['publications'];
            $data['statistics']['patents'] = (int)$_POST['patents'];
            $data['statistics']['awards'] = (int)$_POST['awards'];
            $data['statistics']['students_mentored'] = (int)$_POST['students_mentored'];
            break;
            
        case 'update_social':
            $data['social_links']['linkedin'] = $_POST['linkedin'];
            $data['social_links']['twitter'] = $_POST['twitter'];
            $data['social_links']['researchgate'] = $_POST['researchgate'];
            $data['social_links']['orcid'] = $_POST['orcid'];
            $data['social_links']['google_scholar'] = $_POST['google_scholar'];
            break;

        case 'update_thesis':
            $data['thesis']['title'] = $_POST['title'];
            $data['thesis']['year'] = (int)$_POST['year'];
            $data['thesis']['university'] = $_POST['university'];
            $data['thesis']['supervisor'] = $_POST['supervisor'];
            $data['thesis']['pages'] = (int)$_POST['pages'];
            $data['thesis']['abstract'] = $_POST['abstract'];
            $data['thesis']['pdf_file'] = $_POST['pdf_file'];
            $data['thesis']['cover_image'] = $_POST['cover_image'];
            $data['thesis']['keywords'] = array_map('trim', explode(',', $_POST['keywords']));
            break;
    }
    
    // Save updated data
    file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
    $success_message = "Data updated successfully!";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Data Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white !important;
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>Dashboard</h5>
                        <form method="POST" class="d-inline">
                            <button type="submit" name="logout" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </form>
                    </div>
                    
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success alert-sm"><?php echo $success_message; ?></div>
                    <?php endif; ?>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('personal')">
                                <i class="fas fa-user"></i> Personal Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('address')">
                                <i class="fas fa-map-marker-alt"></i> Address
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('contact')">
                                <i class="fas fa-phone"></i> Contact
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('statistics')">
                                <i class="fas fa-chart-bar"></i> Statistics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('social')">
                                <i class="fas fa-share-alt"></i> Social Links
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('education')">
                                <i class="fas fa-graduation-cap"></i> Education
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('publications')">
                                <i class="fas fa-book"></i> Publications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('advisors')">
                                <i class="fas fa-users"></i> Advisors
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('gallery')">
                                <i class="fas fa-images"></i> Gallery
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('videos')">
                                <i class="fas fa-video"></i> Videos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('journey')">
                                <i class="fas fa-road"></i> Journey
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('thesis')">
                                <i class="fas fa-file-alt"></i> Thesis
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Site Data Management</h1>
                </div>

                <!-- Personal Information Section -->
                <div id="personal" class="content-section active">
                    <h3>Personal Information</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_personal">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($data['personal']['name']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($data['personal']['title']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="designation" class="form-label">Designation</label>
                                    <input type="text" class="form-control" id="designation" name="designation" value="<?php echo htmlspecialchars($data['personal']['designation']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="experience_years" class="form-label">Experience Years</label>
                                    <input type="number" class="form-control" id="experience_years" name="experience_years" value="<?php echo $data['personal']['experience_years']; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialization" class="form-label">Specialization</label>
                                    <input type="text" class="form-control" id="specialization" name="specialization" value="<?php echo htmlspecialchars($data['personal']['specialization']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="location" name="location" value="<?php echo htmlspecialchars($data['personal']['location']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($data['personal']['email']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($data['personal']['phone']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="bio_short" class="form-label">Short Bio</label>
                            <textarea class="form-control" id="bio_short" name="bio_short" rows="2"><?php echo htmlspecialchars($data['personal']['bio']['short']); ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="bio_detailed" class="form-label">Detailed Bio</label>
                            <textarea class="form-control" id="bio_detailed" name="bio_detailed" rows="5"><?php echo htmlspecialchars($data['personal']['bio']['detailed']); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Personal Info</button>
                    </form>
                </div>

                <!-- Address Section -->
                <div id="address" class="content-section">
                    <h3>Address Information</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_address">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="line1" class="form-label">Address Line 1</label>
                                    <input type="text" class="form-control" id="line1" name="line1" value="<?php echo htmlspecialchars($data['personal']['address']['line1']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="line2" class="form-label">Address Line 2</label>
                                    <input type="text" class="form-control" id="line2" name="line2" value="<?php echo htmlspecialchars($data['personal']['address']['line2']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city" value="<?php echo htmlspecialchars($data['personal']['address']['city']); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state" value="<?php echo htmlspecialchars($data['personal']['address']['state']); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="country" name="country" value="<?php echo htmlspecialchars($data['personal']['address']['country']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="pincode" class="form-label">Pincode</label>
                            <input type="text" class="form-control" id="pincode" name="pincode" value="<?php echo htmlspecialchars($data['personal']['address']['pincode']); ?>">
                        </div>
                        <button type="submit" class="btn btn-primary">Update Address</button>
                    </form>
                </div>

                <!-- Contact Section -->
                <div id="contact" class="content-section">
                    <h3>Contact Information</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_contact">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="contact_email" name="email" value="<?php echo htmlspecialchars($data['contact']['email']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="contact_phone" name="phone" value="<?php echo htmlspecialchars($data['contact']['phone']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="office_hours" class="form-label">Office Hours</label>
                                    <input type="text" class="form-control" id="office_hours" name="office_hours" value="<?php echo htmlspecialchars($data['contact']['office_hours']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="response_time" class="form-label">Response Time</label>
                                    <input type="text" class="form-control" id="response_time" name="response_time" value="<?php echo htmlspecialchars($data['contact']['response_time']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="preferred_contact" class="form-label">Preferred Contact Method</label>
                            <select class="form-control" id="preferred_contact" name="preferred_contact">
                                <option value="email" <?php echo $data['contact']['preferred_contact'] === 'email' ? 'selected' : ''; ?>>Email</option>
                                <option value="phone" <?php echo $data['contact']['preferred_contact'] === 'phone' ? 'selected' : ''; ?>>Phone</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Contact Info</button>
                    </form>
                </div>

                <!-- Statistics Section -->
                <div id="statistics" class="content-section">
                    <h3>Statistics</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_statistics">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stats_experience_years" class="form-label">Experience Years</label>
                                    <input type="number" class="form-control" id="stats_experience_years" name="experience_years" value="<?php echo $data['statistics']['experience_years']; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="projects_completed" class="form-label">Projects Completed</label>
                                    <input type="number" class="form-control" id="projects_completed" name="projects_completed" value="<?php echo $data['statistics']['projects_completed']; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="publications" class="form-label">Publications</label>
                                    <input type="number" class="form-control" id="publications" name="publications" value="<?php echo $data['statistics']['publications']; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="patents" class="form-label">Patents</label>
                                    <input type="number" class="form-control" id="patents" name="patents" value="<?php echo $data['statistics']['patents']; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="awards" class="form-label">Awards</label>
                                    <input type="number" class="form-control" id="awards" name="awards" value="<?php echo $data['statistics']['awards']; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="students_mentored" class="form-label">Students Mentored</label>
                                    <input type="number" class="form-control" id="students_mentored" name="students_mentored" value="<?php echo $data['statistics']['students_mentored']; ?>">
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Statistics</button>
                    </form>
                </div>

                <!-- Social Links Section -->
                <div id="social" class="content-section">
                    <h3>Social Links</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_social">
                        <div class="mb-3">
                            <label for="linkedin" class="form-label">LinkedIn</label>
                            <input type="url" class="form-control" id="linkedin" name="linkedin" value="<?php echo htmlspecialchars($data['social_links']['linkedin']); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="twitter" class="form-label">Twitter</label>
                            <input type="url" class="form-control" id="twitter" name="twitter" value="<?php echo htmlspecialchars($data['social_links']['twitter']); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="researchgate" class="form-label">ResearchGate</label>
                            <input type="url" class="form-control" id="researchgate" name="researchgate" value="<?php echo htmlspecialchars($data['social_links']['researchgate']); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="orcid" class="form-label">ORCID</label>
                            <input type="url" class="form-control" id="orcid" name="orcid" value="<?php echo htmlspecialchars($data['social_links']['orcid']); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="google_scholar" class="form-label">Google Scholar</label>
                            <input type="url" class="form-control" id="google_scholar" name="google_scholar" value="<?php echo htmlspecialchars($data['social_links']['google_scholar']); ?>">
                        </div>
                        <button type="submit" class="btn btn-primary">Update Social Links</button>
                    </form>
                </div>

                <!-- Array-based sections (redirect to array manager) -->
                <div id="education" class="content-section">
                    <h3>Education Management</h3>
                    <p>Manage education records with add, edit, and delete functionality.</p>
                    <a href="array_manager.php?section=education" class="btn btn-primary">
                        <i class="fas fa-graduation-cap"></i> Manage Education Records
                    </a>
                </div>

                <div id="publications" class="content-section">
                    <h3>Publications Management</h3>
                    <p>Manage publications with add, edit, and delete functionality.</p>
                    <a href="array_manager.php?section=publications" class="btn btn-primary">
                        <i class="fas fa-book"></i> Manage Publications
                    </a>
                </div>

                <div id="advisors" class="content-section">
                    <h3>Advisors Management</h3>
                    <p>Manage advisors with add, edit, and delete functionality.</p>
                    <a href="array_manager.php?section=advisors" class="btn btn-primary">
                        <i class="fas fa-users"></i> Manage Advisors
                    </a>
                </div>

                <div id="gallery" class="content-section">
                    <h3>Gallery Management</h3>
                    <p>Manage gallery items with add, edit, and delete functionality.</p>
                    <a href="array_manager.php?section=gallery" class="btn btn-primary">
                        <i class="fas fa-images"></i> Manage Gallery
                    </a>
                </div>

                <div id="videos" class="content-section">
                    <h3>YouTube Videos Management</h3>
                    <p>Manage YouTube videos with add, edit, and delete functionality.</p>
                    <a href="array_manager.php?section=videos" class="btn btn-primary">
                        <i class="fas fa-video"></i> Manage Videos
                    </a>
                </div>

                <div id="journey" class="content-section">
                    <h3>Journey Management</h3>
                    <p>Manage journey timeline with add, edit, and delete functionality.</p>
                    <a href="array_manager.php?section=journey" class="btn btn-primary">
                        <i class="fas fa-road"></i> Manage Journey
                    </a>
                </div>

                <div id="thesis" class="content-section">
                    <h3>Thesis Information</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_thesis">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="thesis_title" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="thesis_title" name="title" value="<?php echo htmlspecialchars($data['thesis']['title']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="thesis_year" class="form-label">Year</label>
                                    <input type="number" class="form-control" id="thesis_year" name="year" value="<?php echo $data['thesis']['year']; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="university" class="form-label">University</label>
                                    <input type="text" class="form-control" id="university" name="university" value="<?php echo htmlspecialchars($data['thesis']['university']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supervisor" class="form-label">Supervisor</label>
                                    <input type="text" class="form-control" id="supervisor" name="supervisor" value="<?php echo htmlspecialchars($data['thesis']['supervisor']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pages" class="form-label">Pages</label>
                                    <input type="number" class="form-control" id="pages" name="pages" value="<?php echo $data['thesis']['pages']; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pdf_file" class="form-label">PDF File Path</label>
                                    <input type="text" class="form-control" id="pdf_file" name="pdf_file" value="<?php echo htmlspecialchars($data['thesis']['pdf_file']); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="thesis_abstract" class="form-label">Abstract</label>
                            <textarea class="form-control" id="thesis_abstract" name="abstract" rows="4"><?php echo htmlspecialchars($data['thesis']['abstract']); ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="cover_image" class="form-label">Cover Image Path</label>
                            <input type="text" class="form-control" id="cover_image" name="cover_image" value="<?php echo htmlspecialchars($data['thesis']['cover_image']); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="keywords" class="form-label">Keywords (comma separated)</label>
                            <input type="text" class="form-control" id="keywords" name="keywords" value="<?php echo htmlspecialchars(implode(', ', $data['thesis']['keywords'])); ?>">
                        </div>
                        <button type="submit" class="btn btn-primary">Update Thesis Info</button>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked nav link
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
