<?php
/**
 * Test Gallery Manager Functionality
 */

session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/Security.php';

$auth = new Auth($pdo);

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

$json_file = '../data/site-data.json';

try {
    // Load current data
    if (!file_exists($json_file)) {
        throw new Exception("JSON data file not found: " . $json_file);
    }
    
    $jsonContent = file_get_contents($json_file);
    $data = json_decode($jsonContent, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Error parsing JSON: " . json_last_error_msg());
    }
    
    $gallery_data = $data['gallery'] ?? [];
    $test_results = [];
    
    // Test 1: Check if gallery data exists
    $test_results[] = [
        'test' => 'Gallery data exists',
        'result' => !empty($gallery_data) ? 'PASS' : 'FAIL',
        'details' => count($gallery_data) . ' gallery items found'
    ];
    
    // Test 2: Check gallery data structure
    $structure_valid = true;
    $required_fields = ['id', 'title', 'category', 'type', 'image', 'description', 'link'];
    
    foreach ($gallery_data as $index => $item) {
        foreach ($required_fields as $field) {
            if (!isset($item[$field])) {
                $structure_valid = false;
                break 2;
            }
        }
    }
    
    $test_results[] = [
        'test' => 'Gallery data structure',
        'result' => $structure_valid ? 'PASS' : 'FAIL',
        'details' => $structure_valid ? 'All required fields present' : 'Missing required fields'
    ];
    
    // Test 3: Check for HTML entities
    $has_entities = false;
    $entity_count = 0;
    
    foreach ($gallery_data as $item) {
        foreach (['title', 'description'] as $field) {
            if (isset($item[$field]) && (strpos($item[$field], '&#039;') !== false || strpos($item[$field], '&quot;') !== false)) {
                $has_entities = true;
                $entity_count++;
            }
        }
    }
    
    $test_results[] = [
        'test' => 'HTML entities check',
        'result' => !$has_entities ? 'PASS' : 'WARNING',
        'details' => $has_entities ? "$entity_count fields contain HTML entities" : 'No HTML entities found'
    ];
    
    // Test 4: Check file permissions
    $json_writable = is_writable($json_file);
    $test_results[] = [
        'test' => 'JSON file writable',
        'result' => $json_writable ? 'PASS' : 'FAIL',
        'details' => $json_writable ? 'File is writable' : 'File is not writable'
    ];
    
    // Test 5: Check Security class
    $csrf_token = Security::generateCSRFToken();
    $test_results[] = [
        'test' => 'Security class working',
        'result' => !empty($csrf_token) ? 'PASS' : 'FAIL',
        'details' => !empty($csrf_token) ? 'CSRF token generated successfully' : 'Failed to generate CSRF token'
    ];
    
    // Test 6: Check FileUploadWidget class
    $widget_exists = class_exists('FileUploadWidget');
    if (!$widget_exists) {
        try {
            require_once 'components/FileUploadWidget.php';
            $widget_exists = class_exists('FileUploadWidget');
        } catch (Exception $e) {
            // Ignore error for now
        }
    }
    
    $test_results[] = [
        'test' => 'FileUploadWidget class',
        'result' => $widget_exists ? 'PASS' : 'WARNING',
        'details' => $widget_exists ? 'Class loaded successfully' : 'Class not found or error loading'
    ];
    
} catch (Exception $e) {
    $error_message = "Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Manager Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4>Gallery Manager Test Results</h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error_message)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error_message; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Test</th>
                                            <th>Result</th>
                                            <th>Details</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($test_results as $test): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($test['test']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $test['result'] === 'PASS' ? 'success' : 
                                                             ($test['result'] === 'WARNING' ? 'warning' : 'danger'); 
                                                    ?>">
                                                        <?php echo $test['result']; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($test['details']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <?php if (!empty($gallery_data)): ?>
                                <div class="mt-4">
                                    <h5>Sample Gallery Data</h5>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Title</th>
                                                    <th>Category</th>
                                                    <th>Type</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($gallery_data, 0, 5) as $item): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($item['id'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($item['title'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($item['category'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($item['type'] ?? 'N/A'); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <a href="array_manager.php?section=gallery" class="btn btn-primary">Go to Gallery Manager</a>
                            <a href="fix_html_entities.php" class="btn btn-warning">Fix HTML Entities</a>
                            <a href="dashboard_new.php" class="btn btn-secondary">Back to Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
