<?php
session_start();

// Include required files
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/Security.php';

$auth = new Auth($pdo);

// Configure secure session
Security::configureSecureSession();

// Clean expired sessions
$auth->cleanExpiredSessions();

// Handle logout
if (isset($_POST['logout'])) {
    $auth->logout();
    header('Location: login.php');
    exit;
}

// Check authentication
if (!$auth->isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Get current user
$current_user = $auth->getCurrentUser();

// Load current data
$json_file = '../data/site-data.json';
$data = json_decode(file_get_contents($json_file), true);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'update_personal':
            $data['personal']['name'] = Security::sanitizeInput($_POST['name']);
            $data['personal']['title'] = Security::sanitizeInput($_POST['title']);
            $data['personal']['designation'] = Security::sanitizeInput($_POST['designation']);
            $data['personal']['experience_years'] = (int)$_POST['experience_years'];
            $data['personal']['specialization'] = Security::sanitizeInput($_POST['specialization']);
            $data['personal']['location'] = Security::sanitizeInput($_POST['location']);
            $data['personal']['email'] = Security::sanitizeInput($_POST['email']);
            $data['personal']['phone'] = Security::sanitizeInput($_POST['phone']);
            $data['personal']['bio']['short'] = Security::sanitizeInput($_POST['bio_short']);
            $data['personal']['bio']['detailed'] = Security::sanitizeInput($_POST['bio_detailed']);

            // Handle profile image update
            if (!empty($_POST['profile_image'])) {
                $data['personal']['profile_image'] = Security::sanitizeInput($_POST['profile_image']);
                $data['personal']['profile_image_alt'] = Security::sanitizeInput($_POST['profile_image']);
            }

            // Handle about me image update
            if (!empty($_POST['about_image'])) {
                $data['personal']['about_image'] = Security::sanitizeInput($_POST['about_image']);
            }
            break;
            
        case 'update_address':
            $data['personal']['address']['line1'] = $_POST['line1'];
            $data['personal']['address']['line2'] = $_POST['line2'];
            $data['personal']['address']['city'] = $_POST['city'];
            $data['personal']['address']['state'] = $_POST['state'];
            $data['personal']['address']['country'] = $_POST['country'];
            $data['personal']['address']['pincode'] = $_POST['pincode'];
            break;
            
        case 'update_contact':
            $data['contact']['email'] = $_POST['email'];
            $data['contact']['phone'] = $_POST['phone'];
            $data['contact']['office_hours'] = $_POST['office_hours'];
            $data['contact']['response_time'] = $_POST['response_time'];
            $data['contact']['preferred_contact'] = $_POST['preferred_contact'];
            break;
            
        case 'update_statistics':
            $data['statistics']['experience_years'] = (int)$_POST['experience_years'];
            $data['statistics']['projects_completed'] = (int)$_POST['projects_completed'];
            $data['statistics']['publications'] = (int)$_POST['publications'];
            $data['statistics']['patents'] = (int)$_POST['patents'];
            $data['statistics']['awards'] = (int)$_POST['awards'];
            $data['statistics']['students_mentored'] = (int)$_POST['students_mentored'];
            break;
            
        case 'update_social':
            $data['social_links']['linkedin'] = $_POST['linkedin'];
            $data['social_links']['twitter'] = $_POST['twitter'];
            $data['social_links']['researchgate'] = $_POST['researchgate'];
            $data['social_links']['orcid'] = $_POST['orcid'];
            $data['social_links']['google_scholar'] = $_POST['google_scholar'];
            break;
            
        case 'update_thesis':
            $data['thesis']['title'] = $_POST['title'];
            $data['thesis']['year'] = (int)$_POST['year'];
            $data['thesis']['university'] = $_POST['university'];
            $data['thesis']['supervisor'] = $_POST['supervisor'];
            $data['thesis']['pages'] = (int)$_POST['pages'];
            $data['thesis']['abstract'] = $_POST['abstract'];
            $data['thesis']['pdf_file'] = $_POST['pdf_file'];
            $data['thesis']['cover_image'] = $_POST['cover_image'];
            $data['thesis']['keywords'] = array_map('trim', explode(',', $_POST['keywords']));
            break;
    }
    
    // Save updated data
    if (isset($action)) {
        // Create backup
        $backup_file = '../data/backups/site-data-' . date('Y-m-d-H-i-s') . '.json';
        if (!is_dir('../data/backups')) {
            mkdir('../data/backups', 0755, true);
        }
        copy($json_file, $backup_file);
        
        // Save new data
        file_put_contents($json_file, json_encode($data, JSON_PRETTY_PRINT));
        $success_message = "Data updated successfully!";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Data Dashboard</title>
    <link rel="icon" type="image/png" href="../assets/icons/fabicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/icons/fabicon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <?php
    // Include file upload widget CSS
    require_once 'components/FileUploadWidget.php';
    echo '<style>' . FileUploadWidget::getCSS() . '</style>';
    ?>
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white !important;
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
        .user-info {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- Admin Logo -->
                    <div class="admin-logo text-center mb-3 pb-3 border-bottom">
                        <img src="../assets/icons/fabicon.png" alt="Logo" style="width: 40px; height: 40px; border-radius: 50%; object-fit: contain;">
                        <h6 class="mt-2 mb-0 text-primary">Admin Panel</h6>
                    </div>

                    <div class="user-info">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Logged in as:</small>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="showSection('profile')"><i class="fas fa-user-edit"></i> Profile</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="showSection('change-password')"><i class="fas fa-key"></i> Change Password</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" class="d-inline">
                                            <button type="submit" name="logout" class="dropdown-item text-danger">
                                                <i class="fas fa-sign-out-alt"></i> Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <strong><?php echo htmlspecialchars($current_user['name']); ?></strong><br>
                        <small class="text-muted"><?php echo htmlspecialchars($current_user['email']); ?></small>
                    </div>
                    
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success alert-sm"><?php echo $success_message; ?></div>
                    <?php endif; ?>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('personal')">
                                <i class="fas fa-user"></i> Personal Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('address')">
                                <i class="fas fa-map-marker-alt"></i> Address
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('contact')">
                                <i class="fas fa-phone"></i> Contact
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('statistics')">
                                <i class="fas fa-chart-bar"></i> Statistics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('social')">
                                <i class="fas fa-share-alt"></i> Social Links
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('education')">
                                <i class="fas fa-graduation-cap"></i> Education
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('publications')">
                                <i class="fas fa-book"></i> Publications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('thesis')">
                                <i class="fas fa-scroll"></i> Thesis
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('advisors')">
                                <i class="fas fa-users"></i> Advisors
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('gallery')">
                                <i class="fas fa-images"></i> Gallery
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('videos')">
                                <i class="fas fa-video"></i> Videos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('journey')">
                                <i class="fas fa-road"></i> Journey
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('thesis')">
                                <i class="fas fa-scroll"></i> Thesis
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="file_upload.php">
                                <i class="fas fa-upload"></i> File Upload
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="seo_manager.php">
                                <i class="fas fa-search"></i> SEO Manager
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Site Data Management</h1>
                    <div class="text-muted">
                        <small>Last login: <?php echo $current_user['last_login'] ? date('M j, Y g:i A', strtotime($current_user['last_login'])) : 'Never'; ?></small>
                    </div>
                </div>

                <!-- Profile Section -->
                <div id="profile" class="content-section">
                    <h3>Profile Settings</h3>
                    <div id="profile-message"></div>
                    <form id="profile-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profile_name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="profile_name" name="name" value="<?php echo htmlspecialchars($current_user['name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profile_email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="profile_email" name="email" value="<?php echo htmlspecialchars($current_user['email']); ?>" required>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Profile</button>
                    </form>
                </div>

                <!-- Change Password Section -->
                <div id="change-password" class="content-section">
                    <h3>Change Password</h3>
                    <div id="password-message"></div>
                    <form id="password-form">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </form>
                </div>

                <!-- Include all the content sections from the original dashboard -->
                <?php include 'sections/dashboard_content.php'; ?>

            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked nav link
            event.target.classList.add('active');
        }

        // Profile form handler
        document.getElementById('profile-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'update_profile');

            fetch('ajax_handlers.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('profile-message');
                if (data.success) {
                    messageDiv.innerHTML = '<div class="alert alert-success">' + data.message + '</div>';
                    // Update the user info in sidebar
                    setTimeout(() => location.reload(), 1500);
                } else {
                    messageDiv.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('profile-message').innerHTML = '<div class="alert alert-danger">An error occurred</div>';
            });
        });

        // Password form handler
        document.getElementById('password-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (newPassword !== confirmPassword) {
                document.getElementById('password-message').innerHTML = '<div class="alert alert-danger">Passwords do not match</div>';
                return;
            }

            const formData = new FormData(this);
            formData.append('action', 'change_password');

            fetch('ajax_handlers.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('password-message');
                if (data.success) {
                    messageDiv.innerHTML = '<div class="alert alert-success">' + data.message + '</div>';
                    this.reset();
                } else {
                    messageDiv.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('password-message').innerHTML = '<div class="alert alert-danger">An error occurred</div>';
            });
        });

        // File upload widget JavaScript
        <?php echo FileUploadWidget::getJS(); ?>

        // Toast notification function
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.top = '20px';
            toast.style.right = '20px';
            toast.style.zIndex = '9999';
            toast.style.minWidth = '250px';
            toast.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'}"></i> ${message}`;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Image modal function
        function showImageModal(imagePath, imageName) {
            // Create modal if it doesn't exist
            let modal = document.getElementById('imageModal');
            if (!modal) {
                const modalHTML = `
                    <div class="modal fade" id="imageModal" tabindex="-1">
                        <div class="modal-dialog modal-lg modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <img id="modalImage" src="" alt="Preview" class="img-fluid" style="max-height: 70vh;">
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                modal = document.getElementById('imageModal');
            }

            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('imageModalLabel');

            modalImage.src = imagePath;
            modalTitle.textContent = imageName;

            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    </script>
</body>
</html>
