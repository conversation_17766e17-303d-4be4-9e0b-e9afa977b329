<?php
/**
 * DataLoader Class
 * Handles loading and processing of JSON data for the portfolio website
 */

class DataLoader {
    private $data;
    private $jsonFile;
    
    public function __construct($jsonFile = 'data/site-data.json') {
        $this->jsonFile = $jsonFile;
        $this->loadData();
    }
    
    /**
     * Load data from JSON file
     */
    private function loadData() {
        if (!file_exists($this->jsonFile)) {
            throw new Exception("JSON data file not found: " . $this->jsonFile);
        }
        
        $jsonContent = file_get_contents($this->jsonFile);
        $this->data = json_decode($jsonContent, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Error parsing JSON: " . json_last_error_msg());
        }
    }
    
    /**
     * Get all data
     */
    public function getAllData() {
        return $this->data;
    }
    
    /**
     * Get personal information
     */
    public function getPersonal() {
        return $this->data['personal'] ?? [];
    }
    
    /**
     * Get education information
     */
    public function getEducation() {
        return $this->data['education'] ?? [];
    }
    
    /**
     * Get statistics
     */
    public function getStatistics() {
        return $this->data['statistics'] ?? [];
    }
    
    /**
     * Get social links
     */
    public function getSocialLinks() {
        return $this->data['social_links'] ?? [];
    }
    
    /**
     * Get publications
     */
    public function getPublications() {
        return $this->data['publications'] ?? [];
    }
    
    /**
     * Get advisors
     */
    public function getAdvisors() {
        return $this->data['advisors'] ?? [];
    }
    
    /**
     * Get gallery items
     */
    public function getGallery() {
        return $this->data['gallery'] ?? [];
    }
    
    /**
     * Get gallery items by category
     */
    public function getGalleryByCategory($category) {
        $gallery = $this->getGallery();
        return array_filter($gallery, function($item) use ($category) {
            return isset($item['category']) && $item['category'] === $category;
        });
    }
    
    /**
     * Get awards
     */
    public function getAwards() {
        return $this->data['awards'] ?? [];
    }
    
    /**
     * Get journey/timeline
     */
    public function getJourney() {
        return $this->data['journey'] ?? [];
    }
    
    /**
     * Get thesis information
     */
    public function getThesis() {
        return $this->data['thesis'] ?? [];
    }

    /**
     * Get YouTube videos
     */
    public function getYouTubeVideos() {
        return $this->data['youtube_videos'] ?? [];
    }
    
    /**
     * Get contact information
     */
    public function getContact() {
        return $this->data['contact'] ?? [];
    }
    
    /**
     * Get website settings
     */
    public function getWebsiteSettings() {
        return $this->data['website_settings'] ?? [];
    }
    
    /**
     * Get theme colors
     */
    public function getThemeColors() {
        return $this->data['website_settings']['theme'] ?? [];
    }
    
    /**
     * Get SEO settings
     */
    public function getSEO() {
        return $this->data['website_settings']['seo'] ?? [];
    }
    
    /**
     * Helper function to safely get nested array values
     */
    public function get($path, $default = null) {
        $keys = explode('.', $path);
        $value = $this->data;
        
        foreach ($keys as $key) {
            if (!isset($value[$key])) {
                return $default;
            }
            $value = $value[$key];
        }
        
        return $value;
    }
    
    /**
     * Format name for display
     */
    public function getFormattedName() {
        $name = $this->get('personal.name', 'Dr. Jayanta Debbarma');
        $parts = explode(' ', $name);
        
        if (count($parts) >= 3) {
            $title = $parts[0]; // Dr.
            $firstName = $parts[1]; // Jayanta
            $lastName = $parts[2]; // Debbarma
            
            return [
                'full' => $name,
                'title' => $title,
                'first' => $firstName,
                'last' => $lastName,
                'display' => "$title <span class=\"highlight\">$firstName</span><br>$lastName"
            ];
        }
        
        return [
            'full' => $name,
            'title' => '',
            'first' => $name,
            'last' => '',
            'display' => $name
        ];
    }
    
    /**
     * Check if file exists (for images, documents, etc.)
     */
    public function fileExists($path) {
        return file_exists($path);
    }
    
    /**
     * Get safe image path with fallback
     */
    public function getImagePath($path, $fallback = null) {
        if ($this->fileExists($path)) {
            return $path;
        }
        return $fallback;
    }
    
    /**
     * Generate meta tags for SEO
     */
    public function getMetaTags() {
        $seo = $this->getSEO();
        $personal = $this->getPersonal();
        
        $title = $seo['title'] ?? ($personal['name'] ?? 'Portfolio');
        $description = $seo['description'] ?? ($personal['bio']['short'] ?? '');
        $keywords = isset($seo['keywords']) ? implode(', ', $seo['keywords']) : '';
        $author = $seo['author'] ?? ($personal['name'] ?? '');
        
        return [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'author' => $author
        ];
    }
    
    /**
     * Get visitor count from database
     */
    public function getVisitorCount() {
        // This will be handled by the existing visitor_counter.php
        // We'll call it via AJAX or include it
        return 0; // Placeholder
    }
}
?>
