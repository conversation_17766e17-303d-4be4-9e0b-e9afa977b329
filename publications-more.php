<?php
// Include data loader and helper functions
require_once 'includes/DataLoader.php';
require_once 'includes/helpers.php';

// Initialize data loader
try {
    $dataLoader = new DataLoader();
    $personal = $dataLoader->getPersonal();
    $publications = $dataLoader->getPublications();
    $seo = $dataLoader->getMetaTags();
} catch (Exception $e) {
    // Fallback to default values if JSON loading fails
    error_log("Error loading data: " . $e->getMessage());
    $personal = ['name' => 'Dr. Jayanta Debbarma'];
    $publications = [];
    $seo = ['title' => 'Publications - Dr. Jayanta Debbarma'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($seo['title']); ?> - Publications</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Responsive CSS -->
    <link rel="stylesheet" href="assets/css/responsive.css">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #e67e22;
            --accent-light: #f39c12;
            --success-color: #27ae60;
            --text-color: #2c3e50;
            --text-light: #7f8c8d;
            --light-gray: #ecf0f1;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            --gradient-accent: linear-gradient(45deg, #e67e22, #f39c12);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--light-gray);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 100px 0 50px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .back-btn {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.1);
            color: var(--white);
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Main Content */
        .main-content {
            padding: 80px 0;
        }
        
        /* Filter Section */
        .filter-section {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 10px 20px;
            border: 2px solid var(--accent-color);
            background: transparent;
            color: var(--accent-color);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .filter-btn.active,
        .filter-btn:hover {
            background: var(--accent-color);
            color: var(--white);
        }
        
        /* Publications Grid */
        .publications-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .publication-card {
            background: var(--white);
            border-radius: 15px;
            padding: 30px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            border-left: 5px solid var(--accent-color);
        }
        
        .publication-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }
        
        .publication-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .publication-type {
            background: var(--gradient-accent);
            color: var(--white);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .publication-year {
            color: var(--text-light);
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .publication-title {
            color: var(--primary-color);
            font-size: 1.3rem;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .publication-journal {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .publication-authors {
            color: var(--text-light);
            margin-bottom: 15px;
            font-size: 0.95rem;
        }
        
        .publication-abstract {
            color: var(--text-color);
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .publication-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: var(--text-light);
        }
        
        .publication-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--gradient-accent);
            color: var(--white);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary:hover {
            box-shadow: 0 5px 15px rgba(230, 126, 34, 0.4);
        }
        
        .btn-secondary:hover {
            background: var(--primary-color);
            color: var(--white);
        }
        
        /* Statistics Section */
        .stats-section {
            background: var(--white);
            padding: 60px 0;
            border-radius: 20px;
            margin-bottom: 50px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-color);
            display: block;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: var(--text-light);
            font-weight: 500;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .publications-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .publication-actions {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .back-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-flex;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Main Page
        </a>
        <div class="container">
            <h1>Publications & Research</h1>
            <p>Comprehensive collection of academic papers, journals, and research contributions</p>
            <div style="margin-top: 30px;">
                <a href="about-more.php" style="color: white; text-decoration: none; margin: 0 15px;">About</a>
                <a href="journey-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Journey</a>
                <a href="thesis-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Thesis</a>
                <a href="advisors-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Advisors</a>
                <a href="gallery-more.php" style="color: white; text-decoration: none; margin: 0 15px;">Gallery</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Statistics Section -->
            <div class="stats-section" data-aos="fade-up">
                <h2 style="color: var(--primary-color); margin-bottom: 20px;">Publication Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo count($publications) ?: '50+'; ?></span>
                        <span class="stat-label">Total Publications</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">25+</span>
                        <span class="stat-label">Journal Articles</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Conference Papers</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Citations</span>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-section" data-aos="fade-up">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Publications</button>
                    <button class="filter-btn" data-filter="journal">Journal Articles</button>
                    <button class="filter-btn" data-filter="conference">Conference Papers</button>
                    <button class="filter-btn" data-filter="book">Book Chapters</button>
                </div>
            </div>

            <!-- Publications Grid -->
            <div class="publications-grid">
                <?php if (!empty($publications)): ?>
                    <?php $delay = 0; ?>
                    <?php foreach ($publications as $pub): ?>
                        <div class="publication-card" data-aos="zoom-in" data-aos-delay="<?php echo $delay; ?>" data-type="<?php echo escape($pub['type'] ?? 'journal'); ?>">
                            <div class="publication-header">
                                <span class="publication-type"><?php echo escape(ucfirst($pub['type'] ?? 'Journal')); ?></span>
                                <span class="publication-year"><?php echo escape($pub['year']); ?></span>
                            </div>
                            <h3 class="publication-title"><?php echo escape($pub['title']); ?></h3>
                            <div class="publication-journal"><?php echo escape($pub['journal']); ?></div>
                            <?php if (!empty($pub['authors'])): ?>
                                <div class="publication-authors">Authors: <AUTHORS>
                            <?php endif; ?>
                            <p class="publication-abstract"><?php echo escape(truncateText($pub['abstract'], 200)); ?></p>
                            <?php if (!empty($pub['doi']) || !empty($pub['citations'])): ?>
                                <div class="publication-meta">
                                    <?php if (!empty($pub['doi'])): ?>
                                        <span>DOI: <?php echo escape($pub['doi']); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($pub['citations'])): ?>
                                        <span>Citations: <?php echo escape($pub['citations']); ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            <div class="publication-actions">
                                <?php if (!empty($pub['pdf_file'])): ?>
                                    <a href="<?php echo escape($pub['pdf_file']); ?>" class="action-btn btn-primary" target="_blank">
                                        <i class="fas fa-download"></i> Download PDF
                                    </a>
                                <?php endif; ?>
                                <?php if (!empty($pub['doi'])): ?>
                                    <a href="https://doi.org/<?php echo escape($pub['doi']); ?>" class="action-btn btn-secondary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> View Online
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php $delay += 100; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Default publications -->
                    <div class="publication-card" data-aos="zoom-in" data-type="journal">
                        <div class="publication-header">
                            <span class="publication-type">Journal</span>
                            <span class="publication-year">2022</span>
                        </div>
                        <h3 class="publication-title">Advanced Materials for Sustainable Engineering</h3>
                        <div class="publication-journal">Journal of Materials Science</div>
                        <div class="publication-authors">Authors: <AUTHORS>
                        <p class="publication-abstract">This paper explores innovative materials that can reduce environmental impact while maintaining high performance in engineering applications. The research focuses on developing sustainable alternatives to traditional materials.</p>
                        <div class="publication-meta">
                            <span>DOI: 10.1007/s10853-022-07123-4</span>
                            <span>Citations: 45</span>
                        </div>
                        <div class="publication-actions">
                            <a href="#" class="action-btn btn-primary">
                                <i class="fas fa-download"></i> Download PDF
                            </a>
                            <a href="#" class="action-btn btn-secondary">
                                <i class="fas fa-external-link-alt"></i> View Online
                            </a>
                        </div>
                    </div>

                    <div class="publication-card" data-aos="zoom-in" data-aos-delay="100" data-type="conference">
                        <div class="publication-header">
                            <span class="publication-type">Conference</span>
                            <span class="publication-year">2021</span>
                        </div>
                        <h3 class="publication-title">Computational Fluid Dynamics in Modern Engineering</h3>
                        <div class="publication-journal">International Conference on Engineering Applications</div>
                        <div class="publication-authors">Authors: <AUTHORS>
                        <p class="publication-abstract">An in-depth analysis of how computational fluid dynamics has revolutionized design processes in various engineering fields, with case studies and practical applications.</p>
                        <div class="publication-meta">
                            <span>DOI: 10.1109/ICEA.2021.9458123</span>
                            <span>Citations: 32</span>
                        </div>
                        <div class="publication-actions">
                            <a href="#" class="action-btn btn-primary">
                                <i class="fas fa-download"></i> Download PDF
                            </a>
                            <a href="#" class="action-btn btn-secondary">
                                <i class="fas fa-external-link-alt"></i> View Online
                            </a>
                        </div>
                    </div>

                    <div class="publication-card" data-aos="zoom-in" data-aos-delay="200" data-type="journal">
                        <div class="publication-header">
                            <span class="publication-type">Journal</span>
                            <span class="publication-year">2020</span>
                        </div>
                        <h3 class="publication-title">The Future of Renewable Energy Systems</h3>
                        <div class="publication-journal">Energy & Environment Journal</div>
                        <div class="publication-authors">Authors: <AUTHORS>
                        <p class="publication-abstract">Examining emerging technologies and their potential to transform the renewable energy landscape in the coming decades, with focus on integration challenges and solutions.</p>
                        <div class="publication-meta">
                            <span>DOI: 10.1016/j.energy.2020.117456</span>
                            <span>Citations: 67</span>
                        </div>
                        <div class="publication-actions">
                            <a href="#" class="action-btn btn-primary">
                                <i class="fas fa-download"></i> Download PDF
                            </a>
                            <a href="#" class="action-btn btn-secondary">
                                <i class="fas fa-external-link-alt"></i> View Online
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });

        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        const publicationCards = document.querySelectorAll('.publication-card');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                const filterValue = this.getAttribute('data-filter');

                publicationCards.forEach(card => {
                    if (filterValue === 'all' || card.getAttribute('data-type') === filterValue) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
